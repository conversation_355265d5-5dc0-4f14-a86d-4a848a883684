# 第七阶段：查找树与排序算法笔记 - 排序算法

## 📚 排序算法大全

---

## 1. 排序算法概述

### 1.1 排序的定义

#### 定义
**排序**：将一个数据元素的任意序列，重新排列成一个按关键字有序的序列

#### 分类
1. **内部排序**：待排序记录存放在计算机随机存储器中进行的排序
2. **外部排序**：待排序记录的数量很大，以致内存一次不能容纳全部记录

### 1.2 排序算法的稳定性

#### 稳定性定义
如果排序前后，相等关键字的相对位置保持不变，则称该排序算法是稳定的

#### 稳定性分类
- **稳定排序**：冒泡排序、插入排序、归并排序
- **不稳定排序**：选择排序、快速排序、希尔排序、堆排序

### 1.3 排序算法性能对比

| 排序方法 | 平均时间 | 最坏时间 | 最好时间 | 空间复杂度 | 稳定性 |
|----------|----------|----------|----------|------------|--------|
| 冒泡排序 | O(n²) | O(n²) | O(n) | O(1) | 稳定 |
| 选择排序 | O(n²) | O(n²) | O(n²) | O(1) | 不稳定 |
| 插入排序 | O(n²) | O(n²) | O(n) | O(1) | 稳定 |
| 希尔排序 | O(n^1.3) | O(n²) | O(n) | O(1) | 不稳定 |
| 归并排序 | O(n log n) | O(n log n) | O(n log n) | O(n) | 稳定 |
| 快速排序 | O(n log n) | O(n²) | O(n log n) | O(log n) | 不稳定 |
| 堆排序 | O(n log n) | O(n log n) | O(n log n) | O(1) | 不稳定 |

---

## 2. 简单排序算法

### 2.1 冒泡排序

#### 算法思想
重复地走访过要排序的数列，一次比较两个元素，如果顺序错误就把它们交换过来

#### 算法实现
```c
#include <stdio.h>
#define MAXSIZE 20
typedef int KeyType;
typedef struct {
    KeyType key;
    // 其他数据域
} RedType;

typedef struct {
    RedType r[MAXSIZE + 1];  // r[0]闲置或用作哨兵单元
    int length;              // 顺序表长度
} SqList;

// 冒泡排序
void BubbleSort(SqList *L) {
    int i, j;
    for (i = 1; i < L->length; i++) {        // 总共需要n-1趟
        int flag = 0;                        // 标记本趟是否发生交换
        for (j = L->length; j > i; j--) {    // 从后往前冒泡
            if (L->r[j].key < L->r[j-1].key) {
                // 交换r[j]和r[j-1]
                RedType temp = L->r[j];
                L->r[j] = L->r[j-1];
                L->r[j-1] = temp;
                flag = 1;
            }
        }
        if (flag == 0) break;               // 本趟没有发生交换，已有序
    }
}
```

### 2.2 选择排序

#### 算法思想
每一趟从待排序的记录中选出关键字最小的记录，放在已排序序列的末尾

#### 算法实现
```c
// 简单选择排序
void SelectSort(SqList *L) {
    int i, j, k;
    for (i = 1; i < L->length; i++) {        // 选择第i小的记录，并交换到位
        k = i;                               // k记录最小值的位置
        for (j = i + 1; j <= L->length; j++) {
            if (L->r[j].key < L->r[k].key) {
                k = j;                       // k记录目前找到的最小值位置
            }
        }
        if (k != i) {                        // 交换r[i]和r[k]
            RedType temp = L->r[i];
            L->r[i] = L->r[k];
            L->r[k] = temp;
        }
    }
}
```

### 2.3 插入排序

#### 算法思想
将一个记录插入到已经排好序的有序表中，从而得到一个新的、记录数增1的有序表

#### 算法实现
```c
// 直接插入排序
void InsertSort(SqList *L) {
    int i, j;
    for (i = 2; i <= L->length; i++) {
        if (L->r[i].key < L->r[i-1].key) {   // 需要将L->r[i]插入有序子表
            L->r[0] = L->r[i];               // 复制为哨兵
            for (j = i - 1; L->r[0].key < L->r[j].key; j--) {
                L->r[j+1] = L->r[j];         // 记录后移
            }
            L->r[j+1] = L->r[0];             // 插入到正确位置
        }
    }
}
```

---

## 3. 高效排序算法

### 3.1 希尔排序

#### 算法思想
先将整个待排序的记录序列分割成为若干子序列分别进行直接插入排序

#### 算法实现
```c
// 希尔排序
void ShellSort(SqList *L) {
    int dk, i, j;
    for (dk = L->length / 2; dk >= 1; dk = dk / 2) {  // 步长变化
        for (i = dk + 1; i <= L->length; i++) {
            if (L->r[i].key < L->r[i-dk].key) {       // 需要将L->r[i]插入有序增量子表
                L->r[0] = L->r[i];                    // 暂存在L->r[0]
                for (j = i - dk; j > 0 && L->r[0].key < L->r[j].key; j -= dk) {
                    L->r[j+dk] = L->r[j];             // 记录后移，查找插入位置
                }
                L->r[j+dk] = L->r[0];                 // 插入
            }
        }
    }
}
```

### 3.2 快速排序

#### 算法思想
通过一趟排序将待排记录分隔成独立的两部分，其中一部分记录的关键字均比另一部分的关键字小

#### 算法实现
```c
// 快速排序
void QuickSort(SqList *L, int low, int high) {
    if (low < high) {                        // 长度大于1
        int pivotloc = Partition(L, low, high);  // 将L->r[low..high]一分为二
        QuickSort(L, low, pivotloc - 1);     // 对低子表递归排序
        QuickSort(L, pivotloc + 1, high);    // 对高子表递归排序
    }
}

// 一趟快速排序
int Partition(SqList *L, int low, int high) {
    KeyType pivotkey = L->r[low].key;        // 用子表的第一个记录作枢轴记录
    while (low < high) {                     // 从表的两端交替地向中间扫描
        while (low < high && L->r[high].key >= pivotkey) {
            high--;                          // 从high所指位置向前搜索
        }
        L->r[low] = L->r[high];              // 将比枢轴记录小的记录移到低端
        while (low < high && L->r[low].key <= pivotkey) {
            low++;                           // 从low所指位置向后搜索
        }
        L->r[high] = L->r[low];              // 将比枢轴记录大的记录移到高端
    }
    L->r[low] = (RedType){pivotkey};         // 枢轴记录到位
    return low;                              // 返回枢轴位置
}
```

### 3.3 归并排序

#### 算法思想
将两个或两个以上的有序表合并成一个新的有序表

#### 算法实现
```c
// 归并排序
void MergeSort(SqList *L, int low, int high) {
    if (low < high) {
        int mid = (low + high) / 2;          // 将L[low..high]平分为L[low..mid]和L[mid+1..high]
        MergeSort(L, low, mid);              // 对前半部分归并排序
        MergeSort(L, mid + 1, high);         // 对后半部分归并排序
        Merge(L, low, mid, high);            // 将两部分归并
    }
}

// 归并操作
void Merge(SqList *L, int low, int mid, int high) {
    RedType *temp = (RedType*)malloc((high - low + 1) * sizeof(RedType));
    int i = low, j = mid + 1, k = 0;
    
    // 将较小值先放入temp中
    while (i <= mid && j <= high) {
        if (L->r[i].key <= L->r[j].key) {
            temp[k++] = L->r[i++];
        } else {
            temp[k++] = L->r[j++];
        }
    }
    
    // 将剩余元素放入temp中
    while (i <= mid) temp[k++] = L->r[i++];
    while (j <= high) temp[k++] = L->r[j++];
    
    // 将temp中的元素复制回原数组
    for (i = low, k = 0; i <= high; i++, k++) {
        L->r[i] = temp[k];
    }
    
    free(temp);
}
```

### 3.4 堆排序

#### 算法思想
利用堆这种数据结构所设计的一种排序算法

#### 算法实现
```c
// 堆排序
void HeapSort(SqList *L) {
    int i;
    // 把L->r[1..L->length]建成大顶堆
    for (i = L->length / 2; i > 0; i--) {
        HeapAdjust(L, i, L->length);
    }
    
    for (i = L->length; i > 1; i--) {
        // 将堆顶记录和当前未经排序子序列的最后一个记录交换
        RedType temp = L->r[1];
        L->r[1] = L->r[i];
        L->r[i] = temp;
        
        // 将L->r[1..i-1]重新调整为大顶堆
        HeapAdjust(L, 1, i - 1);
    }
}

// 调整堆
void HeapAdjust(SqList *L, int s, int m) {
    RedType rc = L->r[s];
    for (int j = 2 * s; j <= m; j *= 2) {   // 沿key较大的孩子节点向下筛选
        if (j < m && L->r[j].key < L->r[j+1].key) {
            j++;                             // j为key较大的记录的下标
        }
        if (rc.key >= L->r[j].key) break;   // rc应插入在位置s上
        L->r[s] = L->r[j];
        s = j;
    }
    L->r[s] = rc;                           // 插入
}
```

---

## 4. 特殊排序算法

### 4.1 计数排序

#### 算法思想
对于给定的输入序列中的每一个元素x，确定该序列中值小于x的元素的个数

#### 算法实现
```c
// 计数排序（适用于范围较小的整数排序）
void CountingSort(int arr[], int n, int max_val) {
    int *count = (int*)calloc(max_val + 1, sizeof(int));
    int *output = (int*)malloc(n * sizeof(int));
    
    // 统计每个元素出现的次数
    for (int i = 0; i < n; i++) {
        count[arr[i]]++;
    }
    
    // 计算累积计数
    for (int i = 1; i <= max_val; i++) {
        count[i] += count[i - 1];
    }
    
    // 构建输出数组
    for (int i = n - 1; i >= 0; i--) {
        output[count[arr[i]] - 1] = arr[i];
        count[arr[i]]--;
    }
    
    // 复制回原数组
    for (int i = 0; i < n; i++) {
        arr[i] = output[i];
    }
    
    free(count);
    free(output);
}
```

### 4.2 基数排序

#### 算法思想
按照低位先排序，然后收集；再按照高位排序，然后再收集

#### 算法实现
```c
// 基数排序
void RadixSort(int arr[], int n) {
    int max_val = arr[0];
    for (int i = 1; i < n; i++) {
        if (arr[i] > max_val) max_val = arr[i];
    }
    
    // 对每一位进行计数排序
    for (int exp = 1; max_val / exp > 0; exp *= 10) {
        CountingSortByDigit(arr, n, exp);
    }
}

// 按指定位数进行计数排序
void CountingSortByDigit(int arr[], int n, int exp) {
    int output[n];
    int count[10] = {0};
    
    // 统计每个数字出现的次数
    for (int i = 0; i < n; i++) {
        count[(arr[i] / exp) % 10]++;
    }
    
    // 计算累积计数
    for (int i = 1; i < 10; i++) {
        count[i] += count[i - 1];
    }
    
    // 构建输出数组
    for (int i = n - 1; i >= 0; i--) {
        output[count[(arr[i] / exp) % 10] - 1] = arr[i];
        count[(arr[i] / exp) % 10]--;
    }
    
    // 复制回原数组
    for (int i = 0; i < n; i++) {
        arr[i] = output[i];
    }
}
```

---

## 5. 学习要点总结

### 5.1 必须掌握的概念
- [ ] 排序算法的稳定性
- [ ] 内部排序和外部排序的区别
- [ ] 各种排序算法的时间空间复杂度
- [ ] 排序算法的适用场景

### 5.2 必须会做的操作
- [ ] 手工模拟各种排序过程
- [ ] 分析排序算法的时间复杂度
- [ ] 选择合适的排序算法
- [ ] 实现排序算法的代码

### 5.3 常见考试题型
1. **选择题**：排序算法的性质比较
2. **填空题**：排序过程的中间结果
3. **简答题**：排序算法的优缺点分析
4. **算法题**：排序算法的实现和改进

---

## 6. 记忆技巧

### 6.1 稳定性记忆
"**冒插归基稳，选快希堆乱**"
- 稳定：冒泡、插入、归并、基数
- 不稳定：选择、快速、希尔、堆

### 6.2 复杂度记忆
"**平方级：冒选插，对数级：快归堆**"
- O(n²)：冒泡、选择、插入
- O(n log n)：快速、归并、堆

### 6.3 适用场景记忆
"**小数据插入，大数据快排，稳定要归并**"

---

**学习建议**：
1. 重点掌握快速排序和归并排序
2. 理解各种排序算法的适用场景
3. 多练习手工模拟排序过程
4. 掌握排序算法的时间空间复杂度分析

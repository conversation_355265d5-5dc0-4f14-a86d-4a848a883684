# 第五阶段：树结构笔记 - 遍历算法

## 📚 二叉树的遍历

---

## 1. 遍历方法概述

### 1.1 四种遍历方法

#### 1. 先序遍历（DLR）
**顺序**：根节点 → 左子树 → 右子树

#### 2. 中序遍历（LDR）
**顺序**：左子树 → 根节点 → 右子树

#### 3. 后序遍历（LRD）
**顺序**：左子树 → 右子树 → 根节点

#### 4. 层次遍历
**顺序**：从上到下，从左到右

---

## 2. 递归遍历算法

### 2.1 先序遍历
```c
// 递归算法
Status PreOrderTraverse(BiTree T, Status(*Visit)(TElemType e)) {
    if (T) {
        if (Visit(T->data))
            if (PreOrderTraverse(T->lchild, Visit))
                if (PreOrderTraverse(T->rchild, Visit))
                    return OK;
        return ERROR;
    } else return OK;
}
```

### 2.2 中序遍历
```c
// 递归算法
Status InOrderTraverse(BiTree T, Status(*Visit)(TElemType e)) {
    if (T) {
        if (InOrderTraverse(T->lchild, Visit))
            if (Visit(T->data))
                if (InOrderTraverse(T->rchild, Visit))
                    return OK;
        return ERROR;
    } else return OK;
}
```

### 2.3 后序遍历
```c
// 递归算法
Status PostOrderTraverse(BiTree T, Status(*Visit)(TElemType e)) {
    if (T) {
        if (PostOrderTraverse(T->lchild, Visit))
            if (PostOrderTraverse(T->rchild, Visit))
                if (Visit(T->data))
                    return OK;
        return ERROR;
    } else return OK;
}
```

---

## 3. 非递归遍历算法

### 3.1 先序遍历（非递归）
```c
#define MAXSIZE 100

// 非递归算法
Status PreOrderTraverse_Iterative(BiTree T, Status(*Visit)(TElemType e)) {
    BiTree stack[MAXSIZE];
    int top = -1;
    BiTree p = T;
    
    while (p || top != -1) {
        if (p) {
            Visit(p->data);           // 访问节点
            if (p->rchild)            // 右子树入栈
                stack[++top] = p->rchild;
            p = p->lchild;            // 转向左子树
        } else {
            p = stack[top--];         // 退栈
        }
    }
    return OK;
}
```

### 3.2 中序遍历（非递归）
```c
// 非递归算法
Status InOrderTraverse_Iterative(BiTree T, Status(*Visit)(TElemType e)) {
    BiTree stack[MAXSIZE];
    int top = -1;
    BiTree p = T;
    
    while (p || top != -1) {
        if (p) {
            stack[++top] = p;         // 根指针进栈
            p = p->lchild;            // 遍历左子树
        } else {
            p = stack[top--];         // 退栈
            if (!Visit(p->data)) return ERROR;  // 访问根节点
            p = p->rchild;            // 遍历右子树
        }
    }
    return OK;
}
```

### 3.3 后序遍历（非递归）
```c
// 使用标记法的非递归算法
typedef struct {
    BiTree ptr;
    int flag;  // 0表示左子树已访问，1表示右子树已访问
} StackNode;

Status PostOrderTraverse_Iterative(BiTree T, Status(*Visit)(TElemType e)) {
    StackNode stack[MAXSIZE];
    int top = -1;
    BiTree p = T;
    
    while (p || top != -1) {
        if (p) {
            stack[++top].ptr = p;
            stack[top].flag = 0;
            p = p->lchild;
        } else {
            if (stack[top].flag == 0) {
                stack[top].flag = 1;
                p = stack[top].ptr->rchild;
            } else {
                p = stack[top--].ptr;
                if (!Visit(p->data)) return ERROR;
                p = NULL;
            }
        }
    }
    return OK;
}
```

---

## 4. 层次遍历

### 4.1 层次遍历算法
```c
// 使用队列实现层次遍历
Status LevelOrderTraverse(BiTree T, Status(*Visit)(TElemType e)) {
    BiTree queue[MAXSIZE];
    int front = 0, rear = 0;
    
    if (T) {
        queue[rear++] = T;            // 根节点入队
        
        while (front < rear) {
            BiTree p = queue[front++]; // 出队
            if (!Visit(p->data)) return ERROR;
            
            if (p->lchild)            // 左子树入队
                queue[rear++] = p->lchild;
            if (p->rchild)            // 右子树入队
                queue[rear++] = p->rchild;
        }
    }
    return OK;
}
```

---

## 5. 二叉树的重构

### 5.1 重构原理

#### 基本原则
- **先序+中序** 可以唯一确定一棵二叉树
- **后序+中序** 可以唯一确定一棵二叉树
- **先序+后序** 不能唯一确定二叉树（除非是满二叉树）

### 5.2 重构算法

#### 由先序和中序重构二叉树
```c
BiTree BuildTree_PreIn(char pre[], char in[], int preStart, int preEnd, 
                       int inStart, int inEnd) {
    if (preStart > preEnd || inStart > inEnd) return NULL;
    
    // 先序遍历的第一个元素是根节点
    BiTree root = (BiTree)malloc(sizeof(BiTNode));
    root->data = pre[preStart];
    
    // 在中序遍历中找到根节点的位置
    int rootIndex = inStart;
    while (in[rootIndex] != pre[preStart]) {
        rootIndex++;
    }
    
    // 计算左子树节点个数
    int leftSize = rootIndex - inStart;
    
    // 递归构建左右子树
    root->lchild = BuildTree_PreIn(pre, in, preStart + 1, preStart + leftSize,
                                   inStart, rootIndex - 1);
    root->rchild = BuildTree_PreIn(pre, in, preStart + leftSize + 1, preEnd,
                                   rootIndex + 1, inEnd);
    
    return root;
}
```

#### 由后序和中序重构二叉树
```c
BiTree BuildTree_PostIn(char post[], char in[], int postStart, int postEnd,
                        int inStart, int inEnd) {
    if (postStart > postEnd || inStart > inEnd) return NULL;
    
    // 后序遍历的最后一个元素是根节点
    BiTree root = (BiTree)malloc(sizeof(BiTNode));
    root->data = post[postEnd];
    
    // 在中序遍历中找到根节点的位置
    int rootIndex = inStart;
    while (in[rootIndex] != post[postEnd]) {
        rootIndex++;
    }
    
    // 计算左子树节点个数
    int leftSize = rootIndex - inStart;
    
    // 递归构建左右子树
    root->lchild = BuildTree_PostIn(post, in, postStart, postStart + leftSize - 1,
                                    inStart, rootIndex - 1);
    root->rchild = BuildTree_PostIn(post, in, postStart + leftSize, postEnd - 1,
                                    rootIndex + 1, inEnd);
    
    return root;
}
```

---

## 6. 遍历应用示例

### 6.1 完整的测试程序
```c
#include <stdio.h>
#include <stdlib.h>

// 访问函数
Status Visit(TElemType e) {
    printf("%c ", e);
    return OK;
}

// 测试遍历
void TestTraversal() {
    BiTree T;
    printf("请按先序序列输入二叉树（用#表示空节点）：");
    CreateBiTree(&T);
    
    printf("\n先序遍历结果：");
    PreOrderTraverse(T, Visit);
    
    printf("\n中序遍历结果：");
    InOrderTraverse(T, Visit);
    
    printf("\n后序遍历结果：");
    PostOrderTraverse(T, Visit);
    
    printf("\n层次遍历结果：");
    LevelOrderTraverse(T, Visit);
    
    printf("\n");
}
```

---

## 7. 学习要点总结

### 7.1 必须掌握的概念
- [ ] 四种遍历方法的顺序
- [ ] 递归和非递归算法的区别
- [ ] 二叉树重构的原理

### 7.2 必须会做的操作
- [ ] 手工进行二叉树的各种遍历
- [ ] 根据遍历序列重构二叉树
- [ ] 编写非递归遍历算法

### 7.3 常见考试题型
1. **选择题**：遍历结果判断
2. **填空题**：给定树结构求遍历序列
3. **简答题**：遍历算法的时间复杂度
4. **算法题**：二叉树重构、遍历算法实现

---

## 8. 记忆技巧

### 8.1 遍历顺序记忆
"**先中后，根的位置定乾坤**"
- 先序：根在前（DLR）
- 中序：根在中（LDR）
- 后序：根在后（LRD）

### 8.2 重构规律记忆
"**先序定根，中序分割，后序从尾**"
- 先序遍历确定根节点
- 中序遍历分割左右子树
- 后序遍历从末尾确定根节点

---

**学习建议**：
1. 重点掌握递归遍历的思想
2. 理解非递归算法中栈的作用
3. 多练习根据遍历序列重构二叉树
4. 掌握层次遍历中队列的使用

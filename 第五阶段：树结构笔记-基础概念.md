# 第五阶段：树结构笔记 - 基础概念

## 📚 Day 10-11 学习内容

---

## 1. 树的基本概念

### 1.1 树的定义

#### 定义
**树**：是n(n≥0)个节点的有限集合，当n=0时称为空树，否则：
1. 有且仅有一个特定的称为根的节点
2. 其余节点可分为m(m≥0)个互不相交的有限集合T₁, T₂, ..., Tₘ，其中每个集合本身又是一棵树，称为根的子树

#### 基本术语
- **节点的度**：节点拥有的子树个数
- **树的度**：树内各节点度的最大值
- **叶子节点**：度为0的节点
- **分支节点**：度不为0的节点
- **节点的层次**：根节点为第1层，其子节点为第2层，依此类推
- **树的高度（深度）**：树中节点的最大层次
- **森林**：m(m≥0)棵互不相交的树的集合

### 1.2 树的性质

#### 重要性质
1. **节点数与边数关系**：树中节点数 = 边数 + 1
2. **度与节点数关系**：设树T中度为i的节点有nᵢ个，则：
   - 总节点数：n = n₀ + n₁ + n₂ + ... + nₘ
   - 总边数：n-1 = 1×n₁ + 2×n₂ + ... + m×nₘ
3. **叶子节点数**：n₀ = 1 + n₂ + 2×n₃ + ... + (m-1)×nₘ

### 1.3 树的存储结构

#### 1. 双亲表示法
```c
#define MAX_TREE_SIZE 100
typedef struct PTNode {
    TElemType data;    // 节点数据
    int parent;        // 双亲位置域
} PTNode;

typedef struct {
    PTNode nodes[MAX_TREE_SIZE];
    int r, n;          // 根的位置和节点数
} PTree;
```

#### 2. 孩子表示法
```c
// 孩子节点
typedef struct CTNode {
    int child;              // 孩子节点在数组中的位置
    struct CTNode *next;    // 下一个孩子
} *ChildPtr;

// 双亲节点
typedef struct {
    TElemType data;
    ChildPtr firstchild;    // 孩子链表头指针
} CTBox;

typedef struct {
    CTBox nodes[MAX_TREE_SIZE];
    int n, r;              // 节点数和根节点位置
} CTree;
```

#### 3. 孩子兄弟表示法（二叉树表示法）
```c
typedef struct CSNode {
    TElemType data;
    struct CSNode *firstchild, *rightsib;  // 第一个孩子和右兄弟指针
} CSNode, *CSTree;
```

---

## 2. 二叉树基础

### 2.1 二叉树的定义

#### 定义
**二叉树**：是n(n≥0)个节点的有限集合，该集合或者为空集，或者由一个根节点和两棵互不相交的、分别称为根节点的左子树和右子树的二叉树组成

#### 特点
- 每个节点最多有两棵子树
- 子树有左右之分，次序不能颠倒
- 二叉树可以是空集合

### 2.2 二叉树的类型

#### 1. 满二叉树
**定义**：深度为k且有2^k-1个节点的二叉树
**特点**：
- 每一层上的节点数都是最大节点数
- 叶子节点都在最底层

#### 2. 完全二叉树
**定义**：深度为k的二叉树，其前k-1层是满的，第k层从左到右连续
**特点**：
- 叶子节点只能出现在最下两层
- 最下层的叶子节点一定集中在左部连续位置
- 倒数第二层若有叶子节点，一定都在右部连续位置

#### 3. 二叉搜索树（BST）
**定义**：左子树所有节点值 < 根节点值 < 右子树所有节点值

#### 4. 平衡二叉树（AVL树）
**定义**：任何节点的两个子树的高度差不超过1

### 2.3 二叉树的性质

#### 重要性质
1. **层次与节点数**：第i层上最多有2^(i-1)个节点（i≥1）
2. **深度与节点数**：深度为k的二叉树最多有2^k-1个节点
3. **叶子节点与度为2的节点**：n₀ = n₂ + 1
4. **完全二叉树性质**：
   - 具有n个节点的完全二叉树的深度为⌊log₂n⌋+1
   - 节点i的双亲为⌊i/2⌋（i>1）
   - 节点i的左孩子为2i（若2i≤n）
   - 节点i的右孩子为2i+1（若2i+1≤n）

### 2.4 二叉树的存储结构

#### 1. 顺序存储结构
```c
#define MAXSIZE 100
typedef TElemType SqBiTree[MAXSIZE];  // 0号单元存储根节点
```

#### 2. 链式存储结构
```c
typedef struct BiTNode {
    TElemType data;                    // 数据域
    struct BiTNode *lchild, *rchild;   // 左右孩子指针
} BiTNode, *BiTree;
```

#### 3. 三叉链表
```c
typedef struct TriTNode {
    TElemType data;
    struct TriTNode *lchild, *rchild, *parent;  // 左右孩子和双亲指针
} TriTNode, *TriTree;
```

---

## 3. 二叉树的基本操作

### 3.1 创建和销毁

#### 创建二叉树
```c
#include <stdio.h>
#include <stdlib.h>
#define OK 1
#define ERROR 0
typedef int Status;
typedef char TElemType;

typedef struct BiTNode {
    TElemType data;
    struct BiTNode *lchild, *rchild;
} BiTNode, *BiTree;

// 按先序遍历序列创建二叉树
Status CreateBiTree(BiTree *T) {
    TElemType ch;
    scanf("%c", &ch);
    if (ch == '#') {
        *T = NULL;
    } else {
        *T = (BiTree)malloc(sizeof(BiTNode));
        if (!(*T)) return ERROR;
        (*T)->data = ch;                    // 生成根节点
        CreateBiTree(&((*T)->lchild));      // 构造左子树
        CreateBiTree(&((*T)->rchild));      // 构造右子树
    }
    return OK;
}
```

#### 销毁二叉树
```c
Status DestroyBiTree(BiTree *T) {
    if (*T) {
        DestroyBiTree(&((*T)->lchild));
        DestroyBiTree(&((*T)->rchild));
        free(*T);
        *T = NULL;
    }
    return OK;
}
```

### 3.2 基本查询操作

#### 统计节点个数
```c
int NodeCount(BiTree T) {
    if (T == NULL) return 0;
    else return 1 + NodeCount(T->lchild) + NodeCount(T->rchild);
}
```

#### 计算树的深度
```c
int TreeDepth(BiTree T) {
    if (T == NULL) return 0;
    else {
        int leftDepth = TreeDepth(T->lchild);
        int rightDepth = TreeDepth(T->rchild);
        return 1 + (leftDepth > rightDepth ? leftDepth : rightDepth);
    }
}
```

#### 统计叶子节点个数
```c
int LeafCount(BiTree T) {
    if (T == NULL) return 0;
    if (T->lchild == NULL && T->rchild == NULL) return 1;
    else return LeafCount(T->lchild) + LeafCount(T->rchild);
}
```

---

## 4. 学习要点总结

### 4.1 必须掌握的概念
- [ ] 树的基本术语和性质
- [ ] 二叉树的类型和性质
- [ ] 树的存储结构对比
- [ ] 完全二叉树的编号规律

### 4.2 必须会做的计算
- [ ] 计算树的各种参数（深度、节点数等）
- [ ] 完全二叉树的父子关系计算
- [ ] 二叉树性质的应用

### 4.3 常见考试题型
1. **选择题**：树的性质、二叉树类型判断
2. **填空题**：节点个数计算、深度计算
3. **简答题**：树的存储结构比较
4. **算法题**：树的基本操作实现

---

## 5. 记忆技巧

### 5.1 二叉树性质记忆
"**叶子比度二多一，节点总数等边加一**"
- n₀ = n₂ + 1
- 节点数 = 边数 + 1

### 5.2 完全二叉树记忆
"**父亲除二，孩子乘二**"
- 父节点：⌊i/2⌋
- 左孩子：2i
- 右孩子：2i+1

### 5.3 树的度数记忆
"**度数乘以节点数，求和等于边数**"
- ∑(i × nᵢ) = n - 1

---

**学习建议**：
1. 重点理解树和二叉树的区别
2. 掌握二叉树的各种性质
3. 熟练计算完全二叉树的父子关系
4. 理解不同存储结构的优缺点

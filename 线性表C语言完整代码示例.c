#include <stdio.h>
#include <stdlib.h>

// 基本定义
#define MAXSIZE 100
#define OK 1
#define ERROR 0
typedef int Status;
typedef int ElemType;

// ==================== 顺序表的实现 ====================

typedef struct {
    ElemType data[MAXSIZE];  // 存储数据元素的数组
    int length;              // 线性表当前长度
} SqList;

// 顺序表的基本操作
Status InitSqList(SqList *L) {
    L->length = 0;
    return OK;
}

int SqListEmpty(SqList L) {
    return L.length == 0;
}

int SqListLength(SqList L) {
    return L.length;
}

Status GetElem(SqList L, int i, ElemType *e) {
    if (i < 1 || i > L.length) return ERROR;
    *e = L.data[i-1];
    return OK;
}

int LocateElem(SqList L, ElemType e) {
    // 查找值为e的元素，返回其位序
    for (int i = 0; i < L.length; i++) {
        if (L.data[i] == e) return i + 1;         // 返回位序（从1开始）
    }
    return 0;                                     // 未找到
}

Status ListInsert(SqList *L, int i, ElemType e) {
    // 在第i个位置插入元素e
    if (i < 1 || i > L->length + 1) return ERROR;  // 位置不合法
    if (L->length >= MAXSIZE) return ERROR;        // 存储空间已满
    
    for (int j = L->length; j >= i; j--) {         // 元素后移
        L->data[j] = L->data[j-1];
    }
    L->data[i-1] = e;                              // 插入新元素
    L->length++;                                   // 长度加1
    return OK;
}

Status ListDelete(SqList *L, int i, ElemType *e) {
    // 删除第i个位置的元素，用e返回其值
    if (i < 1 || i > L->length) return ERROR;     // 位置不合法
    
    *e = L->data[i-1];                             // 取出被删除元素
    for (int j = i; j < L->length; j++) {          // 元素前移
        L->data[j-1] = L->data[j];
    }
    L->length--;                                   // 长度减1
    return OK;
}

void PrintSqList(SqList L) {
    printf("顺序表内容：");
    for (int i = 0; i < L.length; i++) {
        printf("%d ", L.data[i]);
    }
    printf("\n");
}

// ==================== 单链表的实现 ====================

typedef struct LNode {
    ElemType data;      // 数据域
    struct LNode *next; // 指针域
} LNode, *LinkList;

// 单链表的基本操作
Status InitList(LinkList *L) {
    *L = (LNode*)malloc(sizeof(LNode));  // 创建头节点
    if (!(*L)) return ERROR;             // 内存分配失败
    (*L)->next = NULL;                   // 头节点指针域置空
    return OK;
}

int ListEmpty(LinkList L) {
    return L->next == NULL;
}

int ListLength(LinkList L) {
    int count = 0;
    LNode *p = L->next;
    while (p) {
        count++;
        p = p->next;
    }
    return count;
}

Status GetElemLink(LinkList L, int i, ElemType *e) {
    LNode *p = L->next;
    int j = 1;
    while (p && j < i) {
        p = p->next;
        j++;
    }
    if (!p || j > i) return ERROR;
    *e = p->data;
    return OK;
}

LNode* LocateElem(LinkList L, ElemType e) {
    // 查找值为e的元素
    LNode *p = L->next;         // p指向第一个数据节点
    while (p && p->data != e) {
        p = p->next;
    }
    return p;                   // 找到返回指针，否则返回NULL
}

Status ListInsert(LinkList L, int i, ElemType e) {
    // 在第i个位置插入元素e
    LNode *p = L;               // p指向头节点
    int j = 0;
    
    while (p && j < i-1) {      // 寻找第i-1个节点
        p = p->next;
        j++;
    }
    if (!p || j > i-1) return ERROR;  // 位置不合法
    
    LNode *s = (LNode*)malloc(sizeof(LNode));  // 创建新节点
    if (!s) return ERROR;       // 内存分配失败
    s->data = e;
    s->next = p->next;          // 新节点指向原第i个节点
    p->next = s;                // 第i-1个节点指向新节点
    return OK;
}

Status ListDelete(LinkList L, int i, ElemType *e) {
    // 删除第i个位置的元素
    LNode *p = L;               // p指向头节点
    int j = 0;
    
    while (p->next && j < i-1) { // 寻找第i-1个节点
        p = p->next;
        j++;
    }
    if (!(p->next) || j > i-1) return ERROR;  // 位置不合法
    
    LNode *q = p->next;         // q指向被删除节点
    *e = q->data;               // 保存被删除元素的值
    p->next = q->next;          // 修改指针
    free(q);                    // 释放节点空间
    return OK;
}

void PrintLinkList(LinkList L) {
    printf("链表内容：");
    LNode *p = L->next;
    while (p) {
        printf("%d ", p->data);
        p = p->next;
    }
    printf("\n");
}

void DestroyList(LinkList *L) {
    LNode *p = *L;
    while (p) {
        LNode *q = p;
        p = p->next;
        free(q);
    }
    *L = NULL;
}

// ==================== 应用算法 ====================

// 合并两个有序顺序表
Status MergeSqList(SqList La, SqList Lb, SqList *Lc) {
    int i = 0, j = 0, k = 0;
    
    InitSqList(Lc);
    
    while (i < La.length && j < Lb.length) {
        if (La.data[i] <= Lb.data[j]) {
            Lc->data[k++] = La.data[i++];
        } else {
            Lc->data[k++] = Lb.data[j++];
        }
    }
    
    while (i < La.length) {
        Lc->data[k++] = La.data[i++];
    }
    
    while (j < Lb.length) {
        Lc->data[k++] = Lb.data[j++];
    }
    
    Lc->length = k;
    return OK;
}

// 合并两个有序链表
Status MergeLinkList(LinkList La, LinkList Lb, LinkList *Lc) {
    LNode *pa = La->next;
    LNode *pb = Lb->next;
    LNode *pc;
    
    InitList(Lc);
    pc = *Lc;
    
    while (pa && pb) {
        if (pa->data <= pb->data) {
            LNode *s = (LNode*)malloc(sizeof(LNode));
            s->data = pa->data;
            s->next = NULL;
            pc->next = s;
            pc = s;
            pa = pa->next;
        } else {
            LNode *s = (LNode*)malloc(sizeof(LNode));
            s->data = pb->data;
            s->next = NULL;
            pc->next = s;
            pc = s;
            pb = pb->next;
        }
    }
    
    while (pa) {
        LNode *s = (LNode*)malloc(sizeof(LNode));
        s->data = pa->data;
        s->next = NULL;
        pc->next = s;
        pc = s;
        pa = pa->next;
    }
    
    while (pb) {
        LNode *s = (LNode*)malloc(sizeof(LNode));
        s->data = pb->data;
        s->next = NULL;
        pc->next = s;
        pc = s;
        pb = pb->next;
    }
    
    return OK;
}

// 删除顺序表中所有值为x的元素
Status DeleteAllX(SqList *L, ElemType x) {
    int k = 0;  // 记录不等于x的元素个数
    
    for (int i = 0; i < L->length; i++) {
        if (L->data[i] != x) {
            L->data[k++] = L->data[i];
        }
    }
    
    L->length = k;
    return OK;
}

// 就地逆置单链表
Status ReverseList(LinkList L) {
    LNode *p = L->next;
    L->next = NULL;
    
    while (p) {
        LNode *q = p->next;
        p->next = L->next;
        L->next = p;
        p = q;
    }
    
    return OK;
}

// ==================== 测试函数 ====================

void testSqList() {
    printf("=== 顺序表测试 ===\n");
    
    SqList L;
    InitSqList(&L);
    
    // 插入元素
    printf("插入元素 1, 3, 5, 7, 9\n");
    ListInsert(&L, 1, 1);
    ListInsert(&L, 2, 3);
    ListInsert(&L, 3, 5);
    ListInsert(&L, 4, 7);
    ListInsert(&L, 5, 9);
    PrintSqList(L);
    
    // 在第3个位置插入元素4
    printf("在第3个位置插入元素4\n");
    ListInsert(&L, 3, 4);
    PrintSqList(L);
    
    // 删除第2个元素
    ElemType e;
    printf("删除第2个元素\n");
    ListDelete(&L, 2, &e);
    printf("删除的元素是：%d\n", e);
    PrintSqList(L);
    
    // 查找元素5
    int pos = LocateElem(L, 5);
    printf("元素5的位置：%d\n", pos);
    
    // 删除所有值为5的元素
    printf("删除所有值为5的元素\n");
    DeleteAllX(&L, 5);
    PrintSqList(L);
}

void testLinkList() {
    printf("\n=== 链表测试 ===\n");
    
    LinkList L;
    InitList(&L);
    
    // 插入元素
    printf("插入元素 2, 4, 6, 8, 10\n");
    ListInsert(L, 1, 2);
    ListInsert(L, 2, 4);
    ListInsert(L, 3, 6);
    ListInsert(L, 4, 8);
    ListInsert(L, 5, 10);
    PrintLinkList(L);
    
    // 在第3个位置插入元素5
    printf("在第3个位置插入元素5\n");
    ListInsert(L, 3, 5);
    PrintLinkList(L);
    
    // 删除第4个元素
    ElemType e;
    printf("删除第4个元素\n");
    ListDelete(L, 4, &e);
    printf("删除的元素是：%d\n", e);
    PrintLinkList(L);
    
    // 逆置链表
    printf("逆置链表\n");
    ReverseList(L);
    PrintLinkList(L);
    
    DestroyList(&L);
}

void testMerge() {
    printf("\n=== 合并测试 ===\n");
    
    // 测试顺序表合并
    SqList La, Lb, Lc;
    InitSqList(&La);
    InitSqList(&Lb);
    
    // 创建有序表La: 1, 3, 5, 7
    ListInsert(&La, 1, 1);
    ListInsert(&La, 2, 3);
    ListInsert(&La, 3, 5);
    ListInsert(&La, 4, 7);
    
    // 创建有序表Lb: 2, 4, 6, 8
    ListInsert(&Lb, 1, 2);
    ListInsert(&Lb, 2, 4);
    ListInsert(&Lb, 3, 6);
    ListInsert(&Lb, 4, 8);
    
    printf("有序表La：");
    PrintSqList(La);
    printf("有序表Lb：");
    PrintSqList(Lb);
    
    MergeSqList(La, Lb, &Lc);
    printf("合并后的有序表Lc：");
    PrintSqList(Lc);
}

int main() {
    testSqList();
    testLinkList();
    testMerge();
    return 0;
}

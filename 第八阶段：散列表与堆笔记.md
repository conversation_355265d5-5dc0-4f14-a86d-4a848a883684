# 第八阶段：散列表与堆笔记

## 📚 Day 19-20 学习内容

---

## 1. 散列表（哈希表）

### 1.1 散列表的基本概念

#### 定义
**散列表**：根据关键字而直接进行访问的数据结构，通过散列函数建立关键字和存储地址之间的对应关系

#### 基本术语
- **散列函数**：把查找表中的关键字映射成该关键字对应的地址的函数
- **散列地址**：散列函数计算出来的地址
- **冲突**：不同的关键字映射到同一个散列地址
- **同义词**：具有相同散列地址的不同关键字
- **装填因子**：α = n/m，其中n为表中记录数，m为散列表长度

### 1.2 散列函数的构造方法

#### 1. 直接定址法
```c
// H(key) = a × key + b
int hash_direct(int key, int a, int b) {
    return a * key + b;
}
```

#### 2. 数字分析法
选取关键字中分布较为均匀的若干位作为散列地址

#### 3. 平方取中法
```c
// 取关键字平方后的中间几位
int hash_mid_square(int key, int m) {
    int square = key * key;
    // 提取中间位数，具体实现依据散列表大小
    return (square / 10) % m;  // 简化示例
}
```

#### 4. 折叠法
将关键字分割成位数相同的几部分，然后取它们的叠加和作为散列地址

#### 5. 除留余数法（最常用）
```c
// H(key) = key mod p
int hash_division(int key, int p) {
    return key % p;
}
```

### 1.3 处理冲突的方法

#### 1. 开放定址法

##### 线性探测法
```c
#define HASHSIZE 13
#define NULLKEY -32768

typedef struct {
    int *elem;      // 数据元素存储基址，动态分配数组
    int count;      // 当前数据元素个数
} HashTable;

// 线性探测法插入
int InsertHash_Linear(HashTable *H, int key) {
    int addr = hash_division(key, HASHSIZE);  // 求散列地址
    
    while (H->elem[addr] != NULLKEY) {        // 如果不为空，则冲突
        addr = (addr + 1) % HASHSIZE;         // 开放定址法的线性探测
    }
    H->elem[addr] = key;                      // 直到有空位后插入关键字
    return OK;
}

// 线性探测法查找
int SearchHash_Linear(HashTable H, int key) {
    int addr = hash_division(key, HASHSIZE);  // 求散列地址
    
    while (H.elem[addr] != key) {
        addr = (addr + 1) % HASHSIZE;         // 线性探测
        if (H.elem[addr] == NULLKEY || addr == hash_division(key, HASHSIZE)) {
            return -1;                        // 查找失败
        }
    }
    return addr;                              // 查找成功
}
```

##### 二次探测法
```c
// 二次探测法：di = 1², -1², 2², -2², ...
int InsertHash_Quadratic(HashTable *H, int key) {
    int addr = hash_division(key, HASHSIZE);
    int d = 1;
    
    while (H->elem[addr] != NULLKEY) {
        addr = (hash_division(key, HASHSIZE) + d * d) % HASHSIZE;
        if (++d > HASHSIZE / 2) return ERROR;  // 探测失败
    }
    H->elem[addr] = key;
    return OK;
}
```

##### 双散列法
```c
// 双散列法：di = i × hash2(key)
int hash2(int key) {
    return 7 - (key % 7);  // 第二个散列函数
}

int InsertHash_Double(HashTable *H, int key) {
    int addr = hash_division(key, HASHSIZE);
    int step = hash2(key);
    
    while (H->elem[addr] != NULLKEY) {
        addr = (addr + step) % HASHSIZE;
    }
    H->elem[addr] = key;
    return OK;
}
```

#### 2. 链地址法（拉链法）
```c
typedef struct Node {
    int key;
    struct Node *next;
} Node;

typedef struct {
    Node *head[HASHSIZE];  // 指针数组
    int count;
} ChainHashTable;

// 链地址法插入
int InsertHash_Chain(ChainHashTable *H, int key) {
    int addr = hash_division(key, HASHSIZE);
    Node *newNode = (Node*)malloc(sizeof(Node));
    newNode->key = key;
    newNode->next = H->head[addr];
    H->head[addr] = newNode;
    H->count++;
    return OK;
}

// 链地址法查找
Node* SearchHash_Chain(ChainHashTable H, int key) {
    int addr = hash_division(key, HASHSIZE);
    Node *p = H.head[addr];
    
    while (p && p->key != key) {
        p = p->next;
    }
    return p;  // 找到返回节点指针，否则返回NULL
}

// 链地址法删除
int DeleteHash_Chain(ChainHashTable *H, int key) {
    int addr = hash_division(key, HASHSIZE);
    Node *p = H->head[addr], *prev = NULL;
    
    while (p && p->key != key) {
        prev = p;
        p = p->next;
    }
    
    if (p == NULL) return ERROR;  // 未找到
    
    if (prev == NULL) {           // 删除的是第一个节点
        H->head[addr] = p->next;
    } else {
        prev->next = p->next;
    }
    
    free(p);
    H->count--;
    return OK;
}
```

### 1.4 散列表的性能分析

#### 平均查找长度
- **成功查找的平均查找长度**：ASL_success
- **不成功查找的平均查找长度**：ASL_unsuccess

#### 装填因子的影响
- α越小，冲突越少，但空间利用率低
- α越大，冲突越多，查找时间增长
- 一般控制α在0.6-0.9之间

---

## 2. 堆

### 2.1 堆的基本概念

#### 定义
**堆**：是一棵完全二叉树，且满足堆的性质：
- **大顶堆**：每个节点的值都大于或等于其子节点的值
- **小顶堆**：每个节点的值都小于或等于其子节点的值

#### 堆的存储结构
```c
#define MAXSIZE 100
typedef struct {
    int data[MAXSIZE];  // 存储堆的数组
    int length;         // 堆的当前长度
} Heap;
```

### 2.2 堆的基本操作

#### 1. 堆的调整（向下调整）
```c
// 将以k为根的子树调整为大顶堆
void HeapAdjustDown(Heap *H, int k, int len) {
    int temp = H->data[k];              // 暂存子树的根节点
    
    for (int i = 2 * k; i <= len; i *= 2) {  // 沿key较大的子节点向下筛选
        if (i < len && H->data[i] < H->data[i + 1]) {
            i++;                        // 取key较大的子节点的下标
        }
        if (temp >= H->data[i]) break;  // 筛选结束
        else {
            H->data[k] = H->data[i];    // 将H->data[i]调整到双亲节点上
            k = i;                      // 修改k值，以便继续向下筛选
        }
    }
    H->data[k] = temp;                  // 被筛选节点的值放入最终位置
}
```

#### 2. 建立堆
```c
// 建立大顶堆
void BuildMaxHeap(Heap *H) {
    for (int i = H->length / 2; i > 0; i--) {  // 从最后一个非叶节点开始调整
        HeapAdjustDown(H, i, H->length);
    }
}
```

#### 3. 堆的插入
```c
// 向上调整
void HeapAdjustUp(Heap *H, int k) {
    int temp = H->data[k];
    int i = k / 2;                      // 父节点
    
    while (i > 0 && H->data[i] < temp) {
        H->data[k] = H->data[i];        // 父节点下移
        k = i;
        i = k / 2;                      // 继续向上比较
    }
    H->data[k] = temp;                  // 插入最终位置
}

// 插入元素
int HeapInsert(Heap *H, int x) {
    if (H->length >= MAXSIZE - 1) return ERROR;  // 堆满
    
    H->data[++H->length] = x;           // 新元素放在堆尾
    HeapAdjustUp(H, H->length);         // 向上调整
    return OK;
}
```

#### 4. 堆的删除
```c
// 删除堆顶元素
int HeapDelete(Heap *H, int *x) {
    if (H->length < 1) return ERROR;    // 堆空
    
    *x = H->data[1];                    // 取出堆顶元素
    H->data[1] = H->data[H->length--];  // 用堆尾元素替换堆顶
    HeapAdjustDown(H, 1, H->length);    // 向下调整
    return OK;
}
```

### 2.3 堆排序

#### 堆排序算法
```c
// 堆排序
void HeapSort(int arr[], int n) {
    Heap H;
    H.length = n;
    
    // 将数组复制到堆中（注意下标从1开始）
    for (int i = 0; i < n; i++) {
        H.data[i + 1] = arr[i];
    }
    
    // 建立大顶堆
    BuildMaxHeap(&H);
    
    // 进行n-1次取堆顶和调整
    for (int i = n; i > 1; i--) {
        // 交换堆顶和堆尾元素
        int temp = H.data[1];
        H.data[1] = H.data[i];
        H.data[i] = temp;
        
        // 调整剩余元素为大顶堆
        HeapAdjustDown(&H, 1, i - 1);
    }
    
    // 将结果复制回原数组
    for (int i = 0; i < n; i++) {
        arr[i] = H.data[i + 1];
    }
}
```

### 2.4 优先队列

#### 基于堆的优先队列
```c
typedef struct {
    int data[MAXSIZE];
    int size;
} PriorityQueue;

// 初始化优先队列
void InitPriorityQueue(PriorityQueue *pq) {
    pq->size = 0;
}

// 入队（插入元素）
int Enqueue(PriorityQueue *pq, int x) {
    if (pq->size >= MAXSIZE - 1) return ERROR;
    
    pq->data[++pq->size] = x;
    
    // 向上调整
    int k = pq->size;
    while (k > 1 && pq->data[k] > pq->data[k / 2]) {
        int temp = pq->data[k];
        pq->data[k] = pq->data[k / 2];
        pq->data[k / 2] = temp;
        k = k / 2;
    }
    return OK;
}

// 出队（删除最大元素）
int Dequeue(PriorityQueue *pq, int *x) {
    if (pq->size < 1) return ERROR;
    
    *x = pq->data[1];
    pq->data[1] = pq->data[pq->size--];
    
    // 向下调整
    int k = 1;
    while (2 * k <= pq->size) {
        int i = 2 * k;
        if (i < pq->size && pq->data[i] < pq->data[i + 1]) i++;
        if (pq->data[k] >= pq->data[i]) break;
        
        int temp = pq->data[k];
        pq->data[k] = pq->data[i];
        pq->data[i] = temp;
        k = i;
    }
    return OK;
}
```

---

## 3. 学习要点总结

### 3.1 必须掌握的概念
- [ ] 散列函数的构造方法
- [ ] 冲突处理的两大类方法
- [ ] 装填因子对性能的影响
- [ ] 堆的定义和性质
- [ ] 堆排序的过程

### 3.2 必须会做的操作
- [ ] 手工模拟散列表的插入和查找
- [ ] 计算平均查找长度
- [ ] 手工调整堆的结构
- [ ] 模拟堆排序的过程

### 3.3 常见考试题型
1. **选择题**：散列函数的选择、冲突处理方法
2. **填空题**：平均查找长度计算、堆的性质
3. **简答题**：散列表和堆的优缺点
4. **算法题**：散列表操作、堆的调整

---

## 4. 记忆技巧

### 4.1 散列函数记忆
"**直数平折除，除留最常用**"
- 直接定址、数字分析、平方取中、折叠法、除留余数法

### 4.2 冲突处理记忆
"**开放探测链地址，线性二次双散列**"
- 开放定址法：线性探测、二次探测、双散列
- 链地址法：拉链法

### 4.3 堆的性质记忆
"**大顶父大子，小顶父小子，完全二叉树**"

---

## 5. 性能分析

### 5.1 散列表性能
- **理想情况**：O(1)
- **最坏情况**：O(n)
- **平均情况**：取决于装填因子和冲突处理方法

### 5.2 堆操作性能
- **建堆**：O(n)
- **插入/删除**：O(log n)
- **堆排序**：O(n log n)

---

**学习建议**：
1. 重点掌握除留余数法和链地址法
2. 理解装填因子对散列表性能的影响
3. 熟练掌握堆的调整操作
4. 掌握堆排序的完整过程

# 第五阶段：树结构笔记

## 📚 Day 10-11 学习内容

---

## 1. 树的基本概念

### 1.1 树的定义

#### 定义
**树**：是n(n≥0)个节点的有限集合，当n=0时称为空树，否则：
1. 有且仅有一个特定的称为根的节点
2. 其余节点可分为m(m≥0)个互不相交的有限集合T₁, T₂, ..., Tₘ，其中每个集合本身又是一棵树，称为根的子树

#### 基本术语
- **节点的度**：节点拥有的子树个数
- **树的度**：树内各节点度的最大值
- **叶子节点**：度为0的节点
- **分支节点**：度不为0的节点
- **节点的层次**：根节点为第1层，其子节点为第2层，依此类推
- **树的高度（深度）**：树中节点的最大层次
- **森林**：m(m≥0)棵互不相交的树的集合

### 1.2 树的性质

#### 重要性质
1. **节点数与边数关系**：树中节点数 = 边数 + 1
2. **度与节点数关系**：设树T中度为i的节点有nᵢ个，则：
   - 总节点数：n = n₀ + n₁ + n₂ + ... + nₘ
   - 总边数：n-1 = 1×n₁ + 2×n₂ + ... + m×nₘ
3. **叶子节点数**：n₀ = 1 + n₂ + 2×n₃ + ... + (m-1)×nₘ

### 1.3 树的存储结构

#### 1. 双亲表示法
```c
#define MAX_TREE_SIZE 100
typedef struct PTNode {
    TElemType data;    // 节点数据
    int parent;        // 双亲位置域
} PTNode;

typedef struct {
    PTNode nodes[MAX_TREE_SIZE];
    int r, n;          // 根的位置和节点数
} PTree;
```

#### 2. 孩子表示法
```c
// 孩子节点
typedef struct CTNode {
    int child;              // 孩子节点在数组中的位置
    struct CTNode *next;    // 下一个孩子
} *ChildPtr;

// 双亲节点
typedef struct {
    TElemType data;
    ChildPtr firstchild;    // 孩子链表头指针
} CTBox;

typedef struct {
    CTBox nodes[MAX_TREE_SIZE];
    int n, r;              // 节点数和根节点位置
} CTree;
```

#### 3. 孩子兄弟表示法（二叉树表示法）
```c
typedef struct CSNode {
    TElemType data;
    struct CSNode *firstchild, *rightsib;  // 第一个孩子和右兄弟指针
} CSNode, *CSTree;
```

---

## 2. 二叉树

### 2.1 二叉树的定义

#### 定义
**二叉树**：是n(n≥0)个节点的有限集合，该集合或者为空集，或者由一个根节点和两棵互不相交的、分别称为根节点的左子树和右子树的二叉树组成

#### 特点
- 每个节点最多有两棵子树
- 子树有左右之分，次序不能颠倒
- 二叉树可以是空集合

### 2.2 二叉树的类型

#### 1. 满二叉树
**定义**：深度为k且有2^k-1个节点的二叉树
**特点**：
- 每一层上的节点数都是最大节点数
- 叶子节点都在最底层

#### 2. 完全二叉树
**定义**：深度为k的二叉树，其前k-1层是满的，第k层从左到右连续
**特点**：
- 叶子节点只能出现在最下两层
- 最下层的叶子节点一定集中在左部连续位置
- 倒数第二层若有叶子节点，一定都在右部连续位置

#### 3. 二叉搜索树（BST）
**定义**：左子树所有节点值 < 根节点值 < 右子树所有节点值

#### 4. 平衡二叉树（AVL树）
**定义**：任何节点的两个子树的高度差不超过1

### 2.3 二叉树的性质

#### 重要性质
1. **层次与节点数**：第i层上最多有2^(i-1)个节点（i≥1）
2. **深度与节点数**：深度为k的二叉树最多有2^k-1个节点
3. **叶子节点与度为2的节点**：n₀ = n₂ + 1
4. **完全二叉树性质**：
   - 具有n个节点的完全二叉树的深度为⌊log₂n⌋+1
   - 节点i的双亲为⌊i/2⌋（i>1）
   - 节点i的左孩子为2i（若2i≤n）
   - 节点i的右孩子为2i+1（若2i+1≤n）

### 2.4 二叉树的存储结构

#### 1. 顺序存储结构
```c
#define MAXSIZE 100
typedef TElemType SqBiTree[MAXSIZE];  // 0号单元存储根节点
```

#### 2. 链式存储结构
```c
typedef struct BiTNode {
    TElemType data;                    // 数据域
    struct BiTNode *lchild, *rchild;   // 左右孩子指针
} BiTNode, *BiTree;
```

#### 3. 三叉链表
```c
typedef struct TriTNode {
    TElemType data;
    struct TriTNode *lchild, *rchild, *parent;  // 左右孩子和双亲指针
} TriTNode, *TriTree;
```

### 2.5 二叉树的基本操作

#### 创建二叉树
```c
#include <stdio.h>
#include <stdlib.h>
#define OK 1
#define ERROR 0
typedef int Status;
typedef char TElemType;

typedef struct BiTNode {
    TElemType data;
    struct BiTNode *lchild, *rchild;
} BiTNode, *BiTree;

// 按先序遍历序列创建二叉树
Status CreateBiTree(BiTree *T) {
    TElemType ch;
    scanf("%c", &ch);
    if (ch == '#') {
        *T = NULL;
    } else {
        *T = (BiTree)malloc(sizeof(BiTNode));
        if (!(*T)) return ERROR;
        (*T)->data = ch;                    // 生成根节点
        CreateBiTree(&((*T)->lchild));      // 构造左子树
        CreateBiTree(&((*T)->rchild));      // 构造右子树
    }
    return OK;
}
```

#### 销毁二叉树
```c
Status DestroyBiTree(BiTree *T) {
    if (*T) {
        DestroyBiTree(&((*T)->lchild));
        DestroyBiTree(&((*T)->rchild));
        free(*T);
        *T = NULL;
    }
    return OK;
}
```

---

## 3. 二叉树的遍历

### 3.1 遍历方法

#### 1. 先序遍历（DLR）
**顺序**：根节点 → 左子树 → 右子树

```c
// 递归算法
Status PreOrderTraverse(BiTree T, Status(*Visit)(TElemType e)) {
    if (T) {
        if (Visit(T->data))
            if (PreOrderTraverse(T->lchild, Visit))
                if (PreOrderTraverse(T->rchild, Visit))
                    return OK;
        return ERROR;
    } else return OK;
}

// 非递归算法
Status PreOrderTraverse_Iterative(BiTree T, Status(*Visit)(TElemType e)) {
    BiTree stack[MAXSIZE];
    int top = -1;
    BiTree p = T;
    
    while (p || top != -1) {
        if (p) {
            Visit(p->data);           // 访问节点
            if (p->rchild)            // 右子树入栈
                stack[++top] = p->rchild;
            p = p->lchild;            // 转向左子树
        } else {
            p = stack[top--];         // 退栈
        }
    }
    return OK;
}
```

#### 2. 中序遍历（LDR）
**顺序**：左子树 → 根节点 → 右子树

```c
// 递归算法
Status InOrderTraverse(BiTree T, Status(*Visit)(TElemType e)) {
    if (T) {
        if (InOrderTraverse(T->lchild, Visit))
            if (Visit(T->data))
                if (InOrderTraverse(T->rchild, Visit))
                    return OK;
        return ERROR;
    } else return OK;
}

// 非递归算法
Status InOrderTraverse_Iterative(BiTree T, Status(*Visit)(TElemType e)) {
    BiTree stack[MAXSIZE];
    int top = -1;
    BiTree p = T;
    
    while (p || top != -1) {
        if (p) {
            stack[++top] = p;         // 根指针进栈
            p = p->lchild;            // 遍历左子树
        } else {
            p = stack[top--];         // 退栈
            if (!Visit(p->data)) return ERROR;  // 访问根节点
            p = p->rchild;            // 遍历右子树
        }
    }
    return OK;
}
```

#### 3. 后序遍历（LRD）
**顺序**：左子树 → 右子树 → 根节点

```c
// 递归算法
Status PostOrderTraverse(BiTree T, Status(*Visit)(TElemType e)) {
    if (T) {
        if (PostOrderTraverse(T->lchild, Visit))
            if (PostOrderTraverse(T->rchild, Visit))
                if (Visit(T->data))
                    return OK;
        return ERROR;
    } else return OK;
}

// 非递归算法（使用标记法）
typedef struct {
    BiTree ptr;
    int flag;  // 0表示左子树已访问，1表示右子树已访问
} StackNode;

Status PostOrderTraverse_Iterative(BiTree T, Status(*Visit)(TElemType e)) {
    StackNode stack[MAXSIZE];
    int top = -1;
    BiTree p = T;
    
    while (p || top != -1) {
        if (p) {
            stack[++top].ptr = p;
            stack[top].flag = 0;
            p = p->lchild;
        } else {
            if (stack[top].flag == 0) {
                stack[top].flag = 1;
                p = stack[top].ptr->rchild;
            } else {
                p = stack[top--].ptr;
                if (!Visit(p->data)) return ERROR;
                p = NULL;
            }
        }
    }
    return OK;
}
```

#### 4. 层次遍历
**顺序**：从上到下，从左到右

```c
// 使用队列实现层次遍历
Status LevelOrderTraverse(BiTree T, Status(*Visit)(TElemType e)) {
    BiTree queue[MAXSIZE];
    int front = 0, rear = 0;
    
    if (T) {
        queue[rear++] = T;            // 根节点入队
        
        while (front < rear) {
            BiTree p = queue[front++]; // 出队
            if (!Visit(p->data)) return ERROR;
            
            if (p->lchild)            // 左子树入队
                queue[rear++] = p->lchild;
            if (p->rchild)            // 右子树入队
                queue[rear++] = p->rchild;
        }
    }
    return OK;
}
```

### 3.2 遍历应用

#### 1. 统计节点个数
```c
int NodeCount(BiTree T) {
    if (T == NULL) return 0;
    else return 1 + NodeCount(T->lchild) + NodeCount(T->rchild);
}
```

#### 2. 计算树的深度
```c
int TreeDepth(BiTree T) {
    if (T == NULL) return 0;
    else {
        int leftDepth = TreeDepth(T->lchild);
        int rightDepth = TreeDepth(T->rchild);
        return 1 + (leftDepth > rightDepth ? leftDepth : rightDepth);
    }
}
```

#### 3. 统计叶子节点个数
```c
int LeafCount(BiTree T) {
    if (T == NULL) return 0;
    if (T->lchild == NULL && T->rchild == NULL) return 1;
    else return LeafCount(T->lchild) + LeafCount(T->rchild);
}
```

---

## 4. 二叉树的重构

### 4.1 重构原理

#### 基本原则
- **先序+中序** 可以唯一确定一棵二叉树
- **后序+中序** 可以唯一确定一棵二叉树
- **先序+后序** 不能唯一确定二叉树（除非是满二叉树）

### 4.2 重构算法

#### 由先序和中序重构二叉树
```c
BiTree BuildTree_PreIn(char pre[], char in[], int preStart, int preEnd, 
                       int inStart, int inEnd) {
    if (preStart > preEnd || inStart > inEnd) return NULL;
    
    // 先序遍历的第一个元素是根节点
    BiTree root = (BiTree)malloc(sizeof(BiTNode));
    root->data = pre[preStart];
    
    // 在中序遍历中找到根节点的位置
    int rootIndex = inStart;
    while (in[rootIndex] != pre[preStart]) {
        rootIndex++;
    }
    
    // 计算左子树节点个数
    int leftSize = rootIndex - inStart;
    
    // 递归构建左右子树
    root->lchild = BuildTree_PreIn(pre, in, preStart + 1, preStart + leftSize,
                                   inStart, rootIndex - 1);
    root->rchild = BuildTree_PreIn(pre, in, preStart + leftSize + 1, preEnd,
                                   rootIndex + 1, inEnd);
    
    return root;
}
```

#### 由后序和中序重构二叉树
```c
BiTree BuildTree_PostIn(char post[], char in[], int postStart, int postEnd,
                        int inStart, int inEnd) {
    if (postStart > postEnd || inStart > inEnd) return NULL;
    
    // 后序遍历的最后一个元素是根节点
    BiTree root = (BiTree)malloc(sizeof(BiTNode));
    root->data = post[postEnd];
    
    // 在中序遍历中找到根节点的位置
    int rootIndex = inStart;
    while (in[rootIndex] != post[postEnd]) {
        rootIndex++;
    }
    
    // 计算左子树节点个数
    int leftSize = rootIndex - inStart;
    
    // 递归构建左右子树
    root->lchild = BuildTree_PostIn(post, in, postStart, postStart + leftSize - 1,
                                    inStart, rootIndex - 1);
    root->rchild = BuildTree_PostIn(post, in, postStart + leftSize, postEnd - 1,
                                    rootIndex + 1, inEnd);
    
    return root;
}
```

---

## 5. 线索二叉树

### 5.1 线索二叉树的概念

#### 定义
**线索二叉树**：利用二叉树中空的指针域存放指向节点在某种遍历次序下的前驱和后继节点的指针

#### 线索化规则
- 如果节点的左子树为空，则左指针指向其前驱节点
- 如果节点的右子树为空，则右指针指向其后继节点

### 5.2 线索二叉树的存储结构

```c
typedef enum {Link, Thread} PointerTag;  // Link==0：指针，Thread==1：线索

typedef struct BiThrNode {
    TElemType data;
    struct BiThrNode *lchild, *rchild;   // 左右孩子指针
    PointerTag LTag, RTag;               // 左右标志
} BiThrNode, *BiThrTree;
```

### 5.3 中序线索化

```c
BiThrTree pre;  // 全局变量，始终指向刚刚访问过的节点

// 中序遍历进行中序线索化
void InThreading(BiThrTree p) {
    if (p) {
        InThreading(p->lchild);          // 递归左子树线索化
        
        if (!p->lchild) {                // 建立当前节点的前驱线索
            p->LTag = Thread;
            p->lchild = pre;
        }
        
        if (!pre->rchild) {              // 建立前驱节点的后继线索
            pre->RTag = Thread;
            pre->rchild = p;
        }
        
        pre = p;                         // 保持pre指向p的前驱
        InThreading(p->rchild);          // 递归右子树线索化
    }
}

// 中序线索化二叉树
Status InOrderThreading(BiThrTree *Thrt, BiThrTree T) {
    *Thrt = (BiThrTree)malloc(sizeof(BiThrNode));
    if (!(*Thrt)) return ERROR;
    
    (*Thrt)->LTag = Link;
    (*Thrt)->RTag = Thread;
    (*Thrt)->rchild = *Thrt;             // 右指针回指
    
    if (!T) (*Thrt)->lchild = *Thrt;     // 若二叉树空，则左指针回指
    else {
        (*Thrt)->lchild = T;
        pre = *Thrt;
        InThreading(T);                  // 中序遍历进行中序线索化
        pre->rchild = *Thrt;
        pre->RTag = Thread;              // 最后一个节点线索化
        (*Thrt)->rchild = pre;
    }
    return OK;
}
```

### 5.4 线索二叉树的遍历

```c
// 中序线索二叉树的中序遍历
Status InOrderTraverse_Thr(BiThrTree T, Status(*Visit)(TElemType e)) {
    BiThrTree p = T->lchild;             // p指向根节点
    
    while (p != T) {                     // 空树或遍历结束时，p==T
        while (p->LTag == Link) p = p->lchild;  // 到达最左下角
        
        if (!Visit(p->data)) return ERROR;      // 访问其左子树为空的节点
        
        while (p->RTag == Thread && p->rchild != T) {
            p = p->rchild;
            Visit(p->data);              // 访问后继节点
        }
        
        p = p->rchild;                   // p进至其右子树根
    }
    return OK;
}
```

---

## 6. 最优二叉树（Huffman树）

### 6.1 基本概念

#### 定义
- **路径长度**：从树根到任一节点的路径上分支的数目
- **带权路径长度**：节点的路径长度与节点权值的乘积
- **树的带权路径长度（WPL）**：所有叶子节点的带权路径长度之和
- **最优二叉树**：WPL最小的二叉树

### 6.2 Huffman算法

#### 构造步骤
1. 根据给定的n个权值{w₁, w₂, ..., wₙ}构成n棵二叉树的集合F={T₁, T₂, ..., Tₙ}
2. 在F中选取两棵根节点权值最小的树作为左右子树构造一棵新的二叉树
3. 在F中删除这两棵树，同时将新得到的二叉树加入F中
4. 重复2、3步，直到F中只含一棵树为止

### 6.3 Huffman树的存储结构

```c
typedef struct {
    unsigned int weight;
    unsigned int parent, lchild, rchild;
} HTNode, *HuffmanTree;

typedef char **HuffmanCode;  // 动态分配数组存储Huffman编码表
```

### 6.4 Huffman树的构造算法

```c
// 构造Huffman树
void CreateHuffmanTree(HuffmanTree *HT, int n) {
    if (n <= 1) return;
    
    int m = 2 * n - 1;  // 数组共2n-1个元素
    *HT = (HuffmanTree)malloc((m + 1) * sizeof(HTNode));  // 0号单元未用
    
    // 初始化
    for (int i = 1; i <= m; ++i) {
        (*HT)[i].parent = 0;
        (*HT)[i].lchild = 0;
        (*HT)[i].rchild = 0;
    }
    
    // 输入前n个单元中叶子节点的权值
    for (int i = 1; i <= n; ++i) {
        scanf("%d", &((*HT)[i].weight));
    }
    
    // 建Huffman树
    for (int i = n + 1; i <= m; ++i) {
        // 在HT[1..i-1]中选择parent为0且weight最小的两个节点
        int s1, s2;
        Select(*HT, i - 1, &s1, &s2);
        
        (*HT)[s1].parent = i;
        (*HT)[s2].parent = i;
        (*HT)[i].lchild = s1;
        (*HT)[i].rchild = s2;
        (*HT)[i].weight = (*HT)[s1].weight + (*HT)[s2].weight;
    }
}

// 在HT[1..i]中选择parent为0且weight最小的两个节点，其序号分别为s1和s2
void Select(HuffmanTree HT, int i, int *s1, int *s2) {
    int min1 = INT_MAX, min2 = INT_MAX;
    *s1 = *s2 = 0;
    
    for (int j = 1; j <= i; ++j) {
        if (HT[j].parent == 0) {
            if (HT[j].weight < min1) {
                min2 = min1;
                *s2 = *s1;
                min1 = HT[j].weight;
                *s1 = j;
            } else if (HT[j].weight < min2) {
                min2 = HT[j].weight;
                *s2 = j;
            }
        }
    }
}
```

### 6.5 Huffman编码

```c
// 从叶子到根逆向求出各字符的Huffman编码
void CreateHuffmanCode(HuffmanTree HT, HuffmanCode *HC, int n) {
    *HC = (HuffmanCode)malloc((n + 1) * sizeof(char*));
    char *cd = (char*)malloc(n * sizeof(char));  // 分配求编码的工作空间
    cd[n - 1] = '\0';                            // 编码结束符
    
    for (int i = 1; i <= n; ++i) {               // 逐个字符求Huffman编码
        int start = n - 1;                       // 编码结束符位置
        int c = i;
        int f = HT[i].parent;                    // f指向节点c的双亲节点
        
        while (f != 0) {                         // 从叶子到根逆向求编码
            --start;
            if (HT[f].lchild == c) cd[start] = '0';
            else cd[start] = '1';
            c = f;
            f = HT[f].parent;
        }
        
        (*HC)[i] = (char*)malloc((n - start) * sizeof(char));
        strcpy((*HC)[i], &cd[start]);            // 从cd复制编码串到HC
    }
    
    free(cd);                                    // 释放工作空间
}
```

---

## 7. 学习要点总结

### 7.1 必须掌握的概念
- [ ] 树的基本术语和性质
- [ ] 二叉树的类型和性质
- [ ] 二叉树的四种遍历方法
- [ ] 线索二叉树的概念和作用
- [ ] Huffman树的构造和编码

### 7.2 必须会做的操作
- [ ] 手工进行二叉树的各种遍历
- [ ] 根据遍历序列重构二叉树
- [ ] 计算树的各种参数（深度、节点数等）
- [ ] 构造Huffman树并求编码

### 7.3 常见考试题型
1. **选择题**：树的性质、遍历结果
2. **填空题**：节点个数计算、遍历序列
3. **简答题**：遍历算法、树的应用
4. **算法题**：树的操作、Huffman编码

---

## 8. 自测练习

### 8.1 基础概念题
1. 具有n个节点的完全二叉树的高度是多少？
2. 二叉树的中序遍历有什么特殊性质？
3. 线索二叉树解决了什么问题？

### 8.2 计算题
1. 已知二叉树的先序遍历为ABDECFG，中序遍历为DBEAFCG，画出该二叉树
2. 对于权值序列{5,29,7,8,14,23,3,11}，构造Huffman树并求各字符的编码
3. 计算完全二叉树中编号为i的节点的双亲、左孩子、右孩子的编号

### 8.3 算法题
1. 设计算法求二叉树中值为x的节点的层次
2. 实现二叉树的非递归后序遍历
3. 设计算法判断两棵二叉树是否相似

---

## 9. 记忆技巧

### 9.1 遍历顺序记忆
"**先中后，根的位置定乾坤**"
- 先序：根在前（DLR）
- 中序：根在中（LDR）
- 后序：根在后（LRD）

### 9.2 二叉树性质记忆
"**叶子比度二多一，节点总数等边加一**"
- n₀ = n₂ + 1
- 节点数 = 边数 + 1

### 9.3 完全二叉树记忆
"**父亲除二，孩子乘二**"
- 父节点：⌊i/2⌋
- 左孩子：2i
- 右孩子：2i+1

---

**学习建议**：
1. 重点掌握二叉树的遍历算法
2. 多练习根据遍历序列重构二叉树
3. 理解线索二叉树的线索化过程
4. 掌握Huffman树的构造方法和应用

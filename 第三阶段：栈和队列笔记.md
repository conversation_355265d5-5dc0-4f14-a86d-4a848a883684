# 第三阶段：栈和队列笔记

## 📚 Day 5-6 学习内容

---

## 1. 栈（Stack）

### 1.1 栈的基本概念

#### 定义
**栈**：限定仅在表尾进行插入和删除操作的线性表

#### 特点
- **LIFO**（Last In First Out）：后进先出
- **栈顶**：允许插入和删除的一端
- **栈底**：固定的一端
- **空栈**：不含任何数据元素的栈

#### 基本操作
```
ADT Stack {
    InitStack(&S)       // 初始化栈
    DestroyStack(&S)    // 销毁栈
    ClearStack(&S)      // 清空栈
    StackEmpty(S)       // 判断栈是否为空
    StackLength(S)      // 返回栈的长度
    GetTop(S, &e)       // 返回栈顶元素
    Push(&S, e)         // 入栈
    Pop(&S, &e)         // 出栈
    StackTraverse(S)    // 遍历栈
}
```

### 1.2 栈的顺序存储结构

#### 存储结构定义
```c
#define MAXSIZE 100
#define OK 1
#define ERROR 0
typedef int Status;
typedef int SElemType;

typedef struct {
    SElemType data[MAXSIZE];    // 存储栈元素的数组
    int top;                    // 栈顶指针
} SqStack;
```

#### 基本操作实现

**初始化栈**
```c
Status InitStack(SqStack *S) {
    S->top = -1;                // 栈顶指针初始化为-1
    return OK;
}
```

**判断栈空**
```c
int StackEmpty(SqStack S) {
    return S.top == -1;
}
```

**入栈操作**
```c
Status Push(SqStack *S, SElemType e) {
    if (S->top == MAXSIZE - 1) return ERROR;   // 栈满
    S->top++;                                  // 栈顶指针加1
    S->data[S->top] = e;                       // 新元素入栈
    return OK;
}
```

**出栈操作**
```c
Status Pop(SqStack *S, SElemType *e) {
    if (S->top == -1) return ERROR;            // 栈空
    *e = S->data[S->top];                      // 返回栈顶元素
    S->top--;                                  // 栈顶指针减1
    return OK;
}
```

**获取栈顶元素**
```c
Status GetTop(SqStack S, SElemType *e) {
    if (S.top == -1) return ERROR;             // 栈空
    *e = S.data[S.top];                        // 返回栈顶元素
    return OK;
}
```

### 1.3 栈的链式存储结构

#### 存储结构定义
```c
typedef struct StackNode {
    SElemType data;
    struct StackNode *next;
} StackNode, *LinkStack;
```

#### 基本操作实现

**入栈操作**
```c
Status Push(LinkStack *S, SElemType e) {
    StackNode *p = (StackNode*)malloc(sizeof(StackNode));  // 创建新节点
    if (!p) return ERROR;                      // 内存分配失败
    p->data = e;                               // 设置数据
    p->next = *S;                              // 新节点指向原栈顶
    *S = p;                                    // 更新栈顶指针
    return OK;
}
```

**出栈操作**
```c
Status Pop(LinkStack *S, SElemType *e) {
    if (*S == NULL) return ERROR;              // 栈空
    *e = (*S)->data;                           // 返回栈顶元素
    StackNode *p = *S;                         // 保存栈顶节点
    *S = (*S)->next;                           // 更新栈顶指针
    free(p);                                   // 释放节点
    return OK;
}
```

### 1.4 栈的应用

#### 1. 数制转换
**十进制转八进制算法**：
```c
#include <stdio.h>
void conversion(int N) {
    SqStack S;
    InitStack(&S);

    while (N) {
        Push(&S, N % 8);                       // 余数入栈
        N = N / 8;                             // 更新N
    }

    while (!StackEmpty(S)) {
        int e;
        Pop(&S, &e);                           // 出栈并输出
        printf("%d", e);
    }
}
```

#### 2. 括号匹配检验
```c
#include <string.h>
int BracketMatch(char str[]) {
    SqStack S;
    InitStack(&S);
    int len = strlen(str);

    for (int i = 0; i < len; i++) {
        if (str[i] == '(' || str[i] == '[' || str[i] == '{') {
            Push(&S, str[i]);                  // 左括号入栈
        } else if (str[i] == ')' || str[i] == ']' || str[i] == '}') {
            if (StackEmpty(S)) return 0;       // 栈空，不匹配
            char top;
            Pop(&S, &top);
            if ((str[i] == ')' && top != '(') ||
                (str[i] == ']' && top != '[') ||
                (str[i] == '}' && top != '{')) {
                return 0;                      // 不匹配
            }
        }
    }
    return StackEmpty(S);                      // 栈空则匹配
}
```

#### 3. 表达式求值

**中缀表达式转后缀表达式（逆波兰表达式）**：
```c
#include <ctype.h>
// 运算符优先级函数
int Priority(char op) {
    switch(op) {
        case '+':
        case '-': return 1;
        case '*':
        case '/': return 2;
        default: return 0;
    }
}

void InfixToPostfix(char infix[], char postfix[]) {
    SqStack S;
    InitStack(&S);
    int i = 0, j = 0;
    int len = strlen(infix);

    for (i = 0; i < len; i++) {
        char ch = infix[i];

        if (isalnum(ch)) {                     // 操作数直接输出
            postfix[j++] = ch;
        } else if (ch == '(') {                // 左括号入栈
            Push(&S, ch);
        } else if (ch == ')') {                // 右括号：出栈到左括号
            char top;
            while (!StackEmpty(S)) {
                Pop(&S, &top);
                if (top == '(') break;
                postfix[j++] = top;
            }
        } else {                               // 运算符
            while (!StackEmpty(S)) {
                char top;
                GetTop(S, &top);
                if (top == '(' || Priority(ch) > Priority(top)) {
                    break;
                }
                Pop(&S, &top);
                postfix[j++] = top;
            }
            Push(&S, ch);
        }
    }

    while (!StackEmpty(S)) {                   // 剩余运算符出栈
        char top;
        Pop(&S, &top);
        postfix[j++] = top;
    }

    postfix[j] = '\0';                         // 字符串结束符
}
```

**后缀表达式求值**：
```c
int EvaluatePostfix(char postfix[]) {
    SqStack S;
    InitStack(&S);
    int len = strlen(postfix);

    for (int i = 0; i < len; i++) {
        char ch = postfix[i];

        if (isdigit(ch)) {                     // 操作数入栈
            Push(&S, ch - '0');
        } else {                               // 运算符：取两个操作数计算
            int b, a;
            Pop(&S, &b);
            Pop(&S, &a);

            int result;
            switch (ch) {
                case '+': result = a + b; break;
                case '-': result = a - b; break;
                case '*': result = a * b; break;
                case '/': result = a / b; break;
            }
            Push(&S, result);
        }
    }

    int result;
    Pop(&S, &result);
    return result;
}
```

---

## 2. 队列（Queue）

### 2.1 队列的基本概念

#### 定义
**队列**：只允许在一端进行插入操作，在另一端进行删除操作的线性表

#### 特点
- **FIFO**（First In First Out）：先进先出
- **队头**：允许删除的一端
- **队尾**：允许插入的一端
- **空队列**：不含任何元素的队列

#### 基本操作
```
ADT Queue {
    InitQueue(&Q)       // 初始化队列
    DestroyQueue(&Q)    // 销毁队列
    ClearQueue(&Q)      // 清空队列
    QueueEmpty(Q)       // 判断队列是否为空
    QueueLength(Q)      // 返回队列长度
    GetHead(Q, &e)      // 返回队头元素
    EnQueue(&Q, e)      // 入队
    DeQueue(&Q, &e)     // 出队
    QueueTraverse(Q)    // 遍历队列
}
```

### 2.2 队列的顺序存储结构

#### 循环队列
为了避免"假溢出"现象，采用循环队列：

```c
#define MAXQSIZE 100
typedef int QElemType;

typedef struct {
    QElemType data[MAXQSIZE];   // 存储队列元素的数组
    int front;                  // 队头指针
    int rear;                   // 队尾指针
} SqQueue;
```

#### 基本操作实现

**初始化队列**
```c
Status InitQueue(SqQueue *Q) {
    Q->front = Q->rear = 0;     // 队头队尾指针都指向0
    return OK;
}
```

**判断队列状态**
```c
int QueueEmpty(SqQueue Q) {
    return Q.front == Q.rear;   // 队空条件
}

int QueueFull(SqQueue Q) {
    return (Q.rear + 1) % MAXQSIZE == Q.front;  // 队满条件
}
```

**入队操作**
```c
Status EnQueue(SqQueue *Q, QElemType e) {
    if ((Q->rear + 1) % MAXQSIZE == Q->front) return ERROR;  // 队满
    Q->data[Q->rear] = e;                                    // 新元素入队
    Q->rear = (Q->rear + 1) % MAXQSIZE;                      // 队尾指针后移
    return OK;
}
```

**出队操作**
```c
Status DeQueue(SqQueue *Q, QElemType *e) {
    if (Q->front == Q->rear) return ERROR;                   // 队空
    *e = Q->data[Q->front];                                  // 返回队头元素
    Q->front = (Q->front + 1) % MAXQSIZE;                    // 队头指针后移
    return OK;
}
```

**队列长度**
```c
int QueueLength(SqQueue Q) {
    return (Q.rear - Q.front + MAXQSIZE) % MAXQSIZE;
}
```

### 2.3 队列的链式存储结构

#### 存储结构定义
```c
typedef struct QNode {
    QElemType data;
    struct QNode *next;
} QNode, *QueuePtr;

typedef struct {
    QueuePtr front;             // 队头指针
    QueuePtr rear;              // 队尾指针
} LinkQueue;
```

#### 基本操作实现

**初始化队列**
```c
Status InitQueue(LinkQueue *Q) {
    Q->front = Q->rear = (QNode*)malloc(sizeof(QNode));  // 创建头节点
    if (!Q->front) return ERROR;                         // 内存分配失败
    Q->front->next = NULL;
    return OK;
}
```

**入队操作**
```c
Status EnQueue(LinkQueue *Q, QElemType e) {
    QNode *p = (QNode*)malloc(sizeof(QNode));            // 创建新节点
    if (!p) return ERROR;                                // 内存分配失败
    p->data = e;
    p->next = NULL;
    Q->rear->next = p;                                   // 新节点链接到队尾
    Q->rear = p;                                         // 更新队尾指针
    return OK;
}
```

**出队操作**
```c
Status DeQueue(LinkQueue *Q, QElemType *e) {
    if (Q->front == Q->rear) return ERROR;              // 队空
    QNode *p = Q->front->next;                           // p指向队头元素
    *e = p->data;                                        // 返回队头元素
    Q->front->next = p->next;                            // 修改队头指针
    if (Q->rear == p) Q->rear = Q->front;               // 若队列变空，修改队尾指针
    free(p);                                             // 释放节点
    return OK;
}
```

### 2.4 队列的应用

#### 1. 广度优先搜索（BFS）
```c
// 假设图的相关函数和全局变量已定义
int visited[MAX_VERTEX_NUM];  // 访问标记数组

void BFS(Graph G, int v) {
    SqQueue Q;
    InitQueue(&Q);

    visited[v] = 1;                             // 标记起始顶点已访问
    EnQueue(&Q, v);                             // 起始顶点入队

    while (!QueueEmpty(Q)) {
        int u;
        DeQueue(&Q, &u);                        // 队头顶点出队
        visit(u);                               // 访问顶点u

        for (int w = FirstAdjVex(G, u); w >= 0; w = NextAdjVex(G, u, w)) {
            if (!visited[w]) {                  // 若w未访问
                visited[w] = 1;                 // 标记已访问
                EnQueue(&Q, w);                 // w入队
            }
        }
    }
}
```

#### 2. 打印队列（缓冲区）
操作系统中的打印任务管理

#### 3. 键盘缓冲区
处理键盘输入的缓冲

---

## 3. 栈和队列的比较

### 3.1 相同点
- 都是线性表的特殊情况
- 都是限制插入和删除位置的线性表
- 都可以用顺序存储和链式存储实现

### 3.2 不同点
| 特性 | 栈 | 队列 |
|------|----|----|
| 操作原则 | LIFO（后进先出） | FIFO（先进先出） |
| 操作位置 | 只在栈顶操作 | 队头删除，队尾插入 |
| 应用场景 | 递归、表达式求值 | BFS、缓冲区 |

---

## 4. 学习要点总结

### 4.1 必须掌握的概念
- [ ] 栈和队列的定义和特点
- [ ] LIFO和FIFO的含义
- [ ] 循环队列的设计思想
- [ ] 栈和队列的应用场景

### 4.2 必须会做的操作
- [ ] 手工模拟栈和队列的操作过程
- [ ] 中缀表达式转后缀表达式
- [ ] 后缀表达式求值
- [ ] 循环队列的指针计算

### 4.3 常见考试题型
1. **选择题**：判断栈的输出序列是否合法
2. **填空题**：循环队列的长度计算
3. **简答题**：栈和队列的应用
4. **算法题**：表达式求值、括号匹配

---

## 5. 自测练习

### 5.1 基础概念题
1. 栈和队列的主要区别是什么？
2. 为什么要使用循环队列？
3. 栈在递归中起什么作用？

### 5.2 计算题
1. 栈的输入序列为1,2,3,4,5，哪些输出序列是不可能的？
2. 循环队列容量为6，front=2，rear=5，队列长度是多少？

### 5.3 算法题
1. 用栈实现队列
2. 用队列实现栈
3. 设计算法判断回文字符串

---

## 6. 记忆技巧

### 6.1 栈和队列特点记忆
"**栈像弹夹，队列像排队**"
- 栈：子弹后装先打（LIFO）
- 队列：排队先来先服务（FIFO）

### 6.2 应用场景记忆
"**栈管递归和表达式，队列管搜索和缓冲**"

### 6.3 循环队列公式记忆
"**长度公式：(rear - front + M) % M**"
"**队满条件：(rear + 1) % M == front**"

---

**学习建议**：
1. 重点理解LIFO和FIFO的本质
2. 多练习表达式转换和求值
3. 掌握循环队列的设计思想
4. 结合实际应用理解栈和队列的作用

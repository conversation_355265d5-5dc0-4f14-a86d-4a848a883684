# 第六阶段：图结构笔记 - 基础概念

## 📚 Day 12-14 学习内容

---

## 1. 图的基本概念

### 1.1 图的定义

#### 定义
**图**：是由顶点的有穷非空集合和顶点之间边的集合组成，通常表示为：G(V,E)
- V：顶点(Vertex)集合
- E：边(Edge)集合

#### 基本术语
- **顶点(Vertex)**：图中的数据元素
- **边(Edge)**：顶点之间的关系
- **无向图**：边没有方向的图
- **有向图**：边有方向的图，用箭头表示
- **完全图**：任意两个顶点之间都有边的图
- **稀疏图**：有很少条边或弧的图（e < n log n）
- **稠密图**：有很多条边或弧的图

### 1.2 图的基本术语

#### 度的概念
- **度(Degree)**：与顶点相关联的边的数目
- **入度(InDegree)**：有向图中指向顶点的边数
- **出度(OutDegree)**：有向图中从顶点出发的边数

#### 路径相关
- **路径**：顶点序列，其中每相邻两顶点间都有边
- **路径长度**：路径上边的数目
- **简单路径**：除起点和终点外，其余顶点不重复的路径
- **回路(环)**：起点和终点相同的路径
- **简单回路**：除起点和终点外，其余顶点不重复的回路

#### 连通性
- **连通**：两顶点间存在路径
- **连通图**：图中任意两顶点都连通
- **连通分量**：无向图中的极大连通子图
- **强连通**：有向图中两顶点间存在双向路径
- **强连通图**：有向图中任意两顶点都强连通
- **强连通分量**：有向图中的极大强连通子图

### 1.3 图的性质

#### 重要性质
1. **边数与度数关系**：
   - 无向图：∑degree(vi) = 2e
   - 有向图：∑indegree(vi) = ∑outdegree(vi) = e

2. **完全图的边数**：
   - 无向完全图：e = n(n-1)/2
   - 有向完全图：e = n(n-1)

3. **连通图的最少边数**：n-1条边

---

## 2. 图的存储结构

### 2.1 邻接矩阵表示法

#### 存储结构定义
```c
#define INFINITY INT_MAX    // 最大值∞
#define MAX_VERTEX_NUM 20   // 最大顶点个数
typedef enum {DG, DN, UDG, UDN} GraphKind;  // 图的种类标志

typedef struct ArcCell {
    VRType adj;      // VRType是顶点关系类型。对无权图，用1或0表示相邻否；
                     // 对带权图，则为权值类型
    InfoType *info;  // 该弧相关信息的指针
} ArcCell, AdjMatrix[MAX_VERTEX_NUM][MAX_VERTEX_NUM];

typedef struct {
    VertexType vexs[MAX_VERTEX_NUM];           // 顶点向量
    AdjMatrix arcs;                            // 邻接矩阵
    int vexnum, arcnum;                        // 图的当前顶点数和弧数
    GraphKind kind;                            // 图的种类标志
} MGraph;
```

#### 邻接矩阵的特点
**优点**：
- 直观，易于理解和实现
- 便于检查任意一对顶点间是否存在边
- 便于计算各顶点的度

**缺点**：
- 不便于增加和删除顶点
- 浪费空间，存储稀疏图有大量无效元素
- 浪费时间，统计稀疏图中一共有多少条边

### 2.2 邻接表表示法

#### 存储结构定义
```c
#define MAX_VERTEX_NUM 20

typedef struct ArcNode {
    int adjvex;              // 该弧所指向的顶点的位置
    struct ArcNode *nextarc; // 指向下一条弧的指针
    InfoType *info;          // 该弧相关信息的指针
} ArcNode;

typedef struct VNode {
    VertexType data;         // 顶点信息
    ArcNode *firstarc;       // 指向第一条依附该顶点的弧的指针
} VNode, AdjList[MAX_VERTEX_NUM];

typedef struct {
    AdjList vertices;
    int vexnum, arcnum;      // 图的当前顶点数和弧数
    int kind;                // 图的种类标志
} ALGraph;
```

#### 邻接表的特点
**优点**：
- 便于增加和删除顶点
- 节约稀疏图的空间
- 便于统计边数

**缺点**：
- 不便于检查任意一对顶点间是否存在边
- 不便于计算有向图各顶点的入度

### 2.3 十字链表表示法（有向图）

#### 存储结构定义
```c
typedef struct ArcBox {
    int tailvex, headvex;           // 该弧的尾和头顶点的位置
    struct ArcBox *hlink, *tlink;   // 分别为弧头相同和弧尾相同的弧的链域
    InfoType *info;                 // 该弧相关信息的指针
} ArcBox;

typedef struct VexNode {
    VertexType data;
    ArcBox *firstin, *firstout;     // 分别指向该顶点第一条入弧和出弧
} VexNode;

typedef struct {
    VexNode xlist[MAX_VERTEX_NUM];  // 表头向量
    int vexnum, arcnum;             // 有向图的当前顶点数和弧数
} OLGraph;
```

### 2.4 邻接多重表表示法（无向图）

#### 存储结构定义
```c
typedef struct EBox {
    VisitIf mark;                   // 访问标记
    int ivex, jvex;                 // 该边依附的两个顶点的位置
    struct EBox *ilink, *jlink;     // 分别指向依附这两个顶点的下一条边
    InfoType *info;                 // 该边信息指针
} EBox;

typedef struct VexBox {
    VertexType data;
    EBox *firstedge;                // 指向第一条依附该顶点的边
} VexBox;

typedef struct {
    VexBox adjmulist[MAX_VERTEX_NUM];
    int vexnum, edgenum;            // 无向图的当前顶点数和边数
} AMLGraph;
```

---

## 3. 图的基本操作

### 3.1 邻接矩阵的基本操作

#### 创建无向图
```c
#include <stdio.h>
#include <stdlib.h>
#define MAX_VERTEX_NUM 20
#define INFINITY 32767
typedef char VertexType;
typedef int VRType;

typedef struct {
    VertexType vexs[MAX_VERTEX_NUM];
    VRType arcs[MAX_VERTEX_NUM][MAX_VERTEX_NUM];
    int vexnum, arcnum;
} MGraph;

// 创建无向网
void CreateUDN(MGraph *G) {
    int i, j, k, w;
    VertexType v1, v2;
    
    printf("请输入顶点数和边数：");
    scanf("%d %d", &G->vexnum, &G->arcnum);
    
    printf("请输入%d个顶点：", G->vexnum);
    for (i = 0; i < G->vexnum; i++) {
        scanf(" %c", &G->vexs[i]);
    }
    
    // 初始化邻接矩阵
    for (i = 0; i < G->vexnum; i++) {
        for (j = 0; j < G->vexnum; j++) {
            G->arcs[i][j] = (i == j) ? 0 : INFINITY;
        }
    }
    
    printf("请输入%d条边的信息（顶点1 顶点2 权值）：\n", G->arcnum);
    for (k = 0; k < G->arcnum; k++) {
        scanf(" %c %c %d", &v1, &v2, &w);
        i = LocateVex(*G, v1);
        j = LocateVex(*G, v2);
        G->arcs[i][j] = G->arcs[j][i] = w;  // 无向图对称
    }
}

// 定位顶点在图中的位置
int LocateVex(MGraph G, VertexType v) {
    for (int i = 0; i < G.vexnum; i++) {
        if (G.vexs[i] == v) return i;
    }
    return -1;
}
```

### 3.2 邻接表的基本操作

#### 创建无向图
```c
typedef struct ArcNode {
    int adjvex;
    struct ArcNode *nextarc;
} ArcNode;

typedef struct VNode {
    VertexType data;
    ArcNode *firstarc;
} VNode, AdjList[MAX_VERTEX_NUM];

typedef struct {
    AdjList vertices;
    int vexnum, arcnum;
} ALGraph;

// 创建无向图的邻接表
void CreateUDG(ALGraph *G) {
    int i, j, k;
    VertexType v1, v2;
    ArcNode *p1, *p2;
    
    printf("请输入顶点数和边数：");
    scanf("%d %d", &G->vexnum, &G->arcnum);
    
    // 输入各顶点，构造表头结点表
    for (i = 0; i < G->vexnum; i++) {
        scanf(" %c", &G->vertices[i].data);
        G->vertices[i].firstarc = NULL;
    }
    
    // 输入各边，构造邻接表
    for (k = 0; k < G->arcnum; k++) {
        scanf(" %c %c", &v1, &v2);
        i = LocateVex_AL(*G, v1);
        j = LocateVex_AL(*G, v2);
        
        // 插入边<v1,v2>
        p1 = (ArcNode*)malloc(sizeof(ArcNode));
        p1->adjvex = j;
        p1->nextarc = G->vertices[i].firstarc;
        G->vertices[i].firstarc = p1;
        
        // 插入边<v2,v1>
        p2 = (ArcNode*)malloc(sizeof(ArcNode));
        p2->adjvex = i;
        p2->nextarc = G->vertices[j].firstarc;
        G->vertices[j].firstarc = p2;
    }
}

int LocateVex_AL(ALGraph G, VertexType v) {
    for (int i = 0; i < G.vexnum; i++) {
        if (G.vertices[i].data == v) return i;
    }
    return -1;
}
```

---

## 4. 图的基本算法框架

### 4.1 图的遍历概述

#### 遍历的定义
**图的遍历**：从图中某一顶点出发访遍图中其余顶点，且使每一个顶点仅被访问一次

#### 遍历的特点
- 图的任一顶点都可能和其余所有顶点相邻
- 在访问了某个顶点之后，可能沿着某条路径搜索后又回到该顶点
- 为避免同一顶点被访问多次，需要设置访问标志数组visited[]

### 4.2 访问标志数组
```c
int visited[MAX_VERTEX_NUM];  // 访问标志数组

// 初始化访问标志数组
void InitVisited(int n) {
    for (int i = 0; i < n; i++) {
        visited[i] = 0;  // 0表示未访问，1表示已访问
    }
}
```

---

**本部分总结**：
- 掌握了图的基本概念和术语
- 理解了图的四种主要存储结构
- 学会了图的基本操作实现
- 为后续的图遍历算法打下基础

**下一部分将包含**：
- 深度优先搜索(DFS)
- 广度优先搜索(BFS)
- 图的连通性分析

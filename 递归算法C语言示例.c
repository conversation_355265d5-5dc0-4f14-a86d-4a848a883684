#include <stdio.h>
#include <time.h>

// ==================== 递归算法示例 ====================

// 示例1：计算阶乘
int factorial(int n) {
    if (n == 0 || n == 1) {
        return 1;                    // 递归基
    } else {
        return n * factorial(n-1);   // 递归步
    }
}

// 尾递归版本的阶乘
int factorial_tail(int n, int acc) {
    if (n == 0 || n == 1) {
        return acc;
    } else {
        return factorial_tail(n-1, n * acc);
    }
}

int factorial_optimized(int n) {
    return factorial_tail(n, 1);
}

// 示例2：斐波那契数列（低效递归版本）
int fibonacci(int n) {
    if (n == 0) {
        return 0;                    // 递归基
    }
    if (n == 1) {
        return 1;                    // 递归基
    } else {
        return fibonacci(n-1) + fibonacci(n-2);  // 递归步
    }
}

// 优化版本的斐波那契（动态规划）
int fibonacci_dp(int n) {
    if (n == 0) return 0;
    if (n == 1) return 1;
    
    int prev2 = 0, prev1 = 1, current;
    for (int i = 2; i <= n; i++) {
        current = prev1 + prev2;
        prev2 = prev1;
        prev1 = current;
    }
    return current;
}

// 示例3：汉诺塔问题
void hanoi(int n, char from, char to, char aux) {
    if (n == 1) {
        printf("将盘子 %d 从 %c 移动到 %c\n", n, from, to);
    } else {
        hanoi(n-1, from, aux, to);    // 将n-1个盘子从from移到aux
        printf("将盘子 %d 从 %c 移动到 %c\n", n, from, to);  // 将第n个盘子从from移到to
        hanoi(n-1, aux, to, from);    // 将n-1个盘子从aux移到to
    }
}

// 示例4：二分查找（递归版本）
int binary_search(int arr[], int left, int right, int target) {
    if (left > right) {
        return -1;  // 未找到
    }
    
    int mid = left + (right - left) / 2;
    
    if (arr[mid] == target) {
        return mid;  // 找到目标
    } else if (arr[mid] > target) {
        return binary_search(arr, left, mid-1, target);   // 在左半部分查找
    } else {
        return binary_search(arr, mid+1, right, target);  // 在右半部分查找
    }
}

// 示例5：计算x的n次幂
double power(double x, int n) {
    if (n == 0) {
        return 1.0;
    }
    if (n == 1) {
        return x;
    }
    
    if (n % 2 == 0) {
        double half = power(x, n/2);
        return half * half;          // 偶数次幂优化
    } else {
        return x * power(x, n-1);    // 奇数次幂
    }
}

// 示例6：求最大公约数（欧几里得算法）
int gcd(int a, int b) {
    if (b == 0) {
        return a;                    // 递归基
    } else {
        return gcd(b, a % b);        // 递归步
    }
}

// 示例7：数组求和
int array_sum(int arr[], int n) {
    if (n == 0) {
        return 0;                    // 递归基
    } else {
        return arr[n-1] + array_sum(arr, n-1);  // 递归步
    }
}

// 示例8：字符串长度计算
int string_length(char str[]) {
    if (str[0] == '\0') {
        return 0;                    // 递归基
    } else {
        return 1 + string_length(str + 1);  // 递归步
    }
}

// 示例9：判断回文字符串
int is_palindrome(char str[], int start, int end) {
    if (start >= end) {
        return 1;                    // 递归基：单个字符或空串是回文
    }
    
    if (str[start] != str[end]) {
        return 0;                    // 首尾字符不同，不是回文
    }
    
    return is_palindrome(str, start+1, end-1);  // 递归检查内部子串
}

// 示例10：数字转换为字符串（递归版本）
void number_to_string(int n) {
    if (n == 0) {
        return;                      // 递归基
    }
    
    number_to_string(n / 10);        // 先处理高位
    printf("%d", n % 10);            // 再输出当前位
}

// ==================== 复杂度分析示例 ====================

// 简单循环 - O(n)
void simple_loop(int n) {
    for (int i = 0; i < n; i++) {
        printf("%d ", i);
    }
    printf("\n");
}

// 嵌套循环 - O(n²)
void nested_loop(int n) {
    for (int i = 0; i < n; i++) {
        for (int j = 0; j < n; j++) {
            printf("(%d,%d) ", i, j);
        }
    }
    printf("\n");
}

// 对数时间复杂度 - O(log n)
int binary_search_iterative(int arr[], int n, int target) {
    int left = 0, right = n - 1;
    
    while (left <= right) {
        int mid = left + (right - left) / 2;
        
        if (arr[mid] == target) {
            return mid;
        } else if (arr[mid] < target) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }
    
    return -1;
}

// ==================== 测试函数 ====================

void test_factorial() {
    printf("=== 阶乘测试 ===\n");
    
    for (int i = 0; i <= 10; i++) {
        printf("factorial(%d) = %d\n", i, factorial(i));
    }
    
    printf("\n尾递归优化版本：\n");
    for (int i = 0; i <= 10; i++) {
        printf("factorial_optimized(%d) = %d\n", i, factorial_optimized(i));
    }
    printf("\n");
}

void test_fibonacci() {
    printf("=== 斐波那契数列测试 ===\n");
    
    printf("递归版本（较慢）：\n");
    clock_t start = clock();
    for (int i = 0; i <= 10; i++) {
        printf("fibonacci(%d) = %d\n", i, fibonacci(i));
    }
    clock_t end = clock();
    printf("递归版本耗时：%f 秒\n", ((double)(end - start)) / CLOCKS_PER_SEC);
    
    printf("\n动态规划版本（较快）：\n");
    start = clock();
    for (int i = 0; i <= 30; i++) {
        printf("fibonacci_dp(%d) = %d\n", i, fibonacci_dp(i));
    }
    end = clock();
    printf("动态规划版本耗时：%f 秒\n", ((double)(end - start)) / CLOCKS_PER_SEC);
    printf("\n");
}

void test_hanoi() {
    printf("=== 汉诺塔测试 ===\n");
    printf("3个盘子的汉诺塔解法：\n");
    hanoi(3, 'A', 'C', 'B');
    printf("\n");
}

void test_binary_search() {
    printf("=== 二分查找测试 ===\n");
    
    int arr[] = {1, 3, 5, 7, 9, 11, 13, 15, 17, 19};
    int n = sizeof(arr) / sizeof(arr[0]);
    
    printf("数组：");
    for (int i = 0; i < n; i++) {
        printf("%d ", arr[i]);
    }
    printf("\n");
    
    int target = 7;
    int result = binary_search(arr, 0, n-1, target);
    if (result != -1) {
        printf("递归版本：元素 %d 在位置 %d\n", target, result);
    } else {
        printf("递归版本：元素 %d 未找到\n", target);
    }
    
    result = binary_search_iterative(arr, n, target);
    if (result != -1) {
        printf("迭代版本：元素 %d 在位置 %d\n", target, result);
    } else {
        printf("迭代版本：元素 %d 未找到\n", target);
    }
    printf("\n");
}

void test_other_algorithms() {
    printf("=== 其他算法测试 ===\n");
    
    // 测试幂运算
    printf("power(2, 10) = %.0f\n", power(2, 10));
    printf("power(3, 4) = %.0f\n", power(3, 4));
    
    // 测试最大公约数
    printf("gcd(48, 18) = %d\n", gcd(48, 18));
    printf("gcd(100, 25) = %d\n", gcd(100, 25));
    
    // 测试数组求和
    int arr[] = {1, 2, 3, 4, 5};
    int n = sizeof(arr) / sizeof(arr[0]);
    printf("数组 {1,2,3,4,5} 的和 = %d\n", array_sum(arr, n));
    
    // 测试字符串长度
    char str[] = "Hello World";
    printf("字符串 \"%s\" 的长度 = %d\n", str, string_length(str));
    
    // 测试回文判断
    char palindrome[] = "racecar";
    char not_palindrome[] = "hello";
    printf("\"%s\" 是回文：%s\n", palindrome, 
           is_palindrome(palindrome, 0, string_length(palindrome)-1) ? "是" : "否");
    printf("\"%s\" 是回文：%s\n", not_palindrome, 
           is_palindrome(not_palindrome, 0, string_length(not_palindrome)-1) ? "是" : "否");
    
    // 测试数字转字符串
    printf("数字 12345 转换为字符串：");
    number_to_string(12345);
    printf("\n\n");
}

void test_complexity() {
    printf("=== 复杂度演示 ===\n");
    
    int n = 5;
    
    printf("简单循环 O(n)：");
    simple_loop(n);
    
    printf("嵌套循环 O(n²)：");
    nested_loop(3);  // 使用较小的n避免输出过多
    
    printf("\n");
}

int main() {
    test_factorial();
    test_fibonacci();
    test_hanoi();
    test_binary_search();
    test_other_algorithms();
    test_complexity();
    
    return 0;
}

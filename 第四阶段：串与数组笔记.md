# 第四阶段：串与数组笔记

## 📚 Day 7-9 学习内容

---

## 1. 串（String）

### 1.1 串的基本概念

#### 定义
**串**：由零个或多个字符组成的有限序列，又称字符串

#### 基本术语
- **空串**：长度为零的串，记作 ""
- **空格串**：由一个或多个空格组成的串
- **子串**：串中任意个连续字符组成的子序列
- **主串**：包含子串的串
- **字符位置**：字符在串中的序号（从1开始）
- **子串位置**：子串第一个字符在主串中的位置

#### 串的抽象数据类型
```
ADT String {
    数据对象：D = {ai | ai ∈ CharacterSet, i=1,2,...,n, n≥0}
    数据关系：R1 = {<ai-1, ai> | ai-1, ai ∈ D, i=2,...,n}
    基本操作：
        StrAssign(&T, chars)     // 串赋值
        StrCopy(&T, S)           // 串复制
        StrEmpty(S)              // 判空
        StrCompare(S, T)         // 串比较
        StrLength(S)             // 求串长
        ClearString(&S)          // 清空串
        Concat(&T, S1, S2)       // 串连接
        SubString(&Sub, S, pos, len)  // 求子串
        Index(S, T, pos)         // 定位子串
        Replace(&S, T, V)        // 串替换
        StrInsert(&S, pos, T)    // 串插入
        StrDelete(&S, pos, len)  // 串删除
}
```

### 1.2 串的存储结构

#### 1. 定长顺序存储
```c
#define MAXSTRLEN 255
typedef unsigned char SString[MAXSTRLEN+1];  // 0号位置存放串的长度
```

#### 2. 堆分配存储
```c
typedef struct {
    char *ch;        // 若是非空串，则按串长分配存储区
    int length;      // 串的当前长度
} HString;
```

#### 3. 块链存储
```c
#define CHUNKSIZE 80  // 块的大小可由用户定义
typedef struct Chunk {
    char ch[CHUNKSIZE];
    struct Chunk *next;
} Chunk;

typedef struct {
    Chunk *head, *tail;  // 串的头和尾指针
    int curlen;          // 串的当前长度
} LString;
```

### 1.3 串的基本操作实现

#### 定长顺序存储的基本操作
```c
#include <stdio.h>
#include <string.h>
#define MAXSTRLEN 255
#define OK 1
#define ERROR 0
typedef int Status;
typedef unsigned char SString[MAXSTRLEN+1];

// 生成一个其值等于chars的串T
Status StrAssign(SString T, char *chars) {
    int i;
    if (strlen(chars) > MAXSTRLEN) return ERROR;
    else {
        T[0] = strlen(chars);
        for (i = 1; i <= T[0]; i++) {
            T[i] = *(chars + i - 1);
        }
        return OK;
    }
}

// 由串S复制得串T
Status StrCopy(SString T, SString S) {
    int i;
    for (i = 0; i <= S[0]; i++) {
        T[i] = S[i];
    }
    return OK;
}

// 若S为空串，则返回TRUE，否则返回FALSE
Status StrEmpty(SString S) {
    return S[0] == 0;
}

// 若S>T，则返回值>0；若S=T，则返回值=0；若S<T，则返回值<0
int StrCompare(SString S, SString T) {
    int i;
    for (i = 1; i <= S[0] && i <= T[0]; i++) {
        if (S[i] != T[i]) return S[i] - T[i];
    }
    return S[0] - T[0];
}

// 返回串的长度
int StrLength(SString S) {
    return S[0];
}

// 用T返回S1和S2联接而成的新串
Status Concat(SString T, SString S1, SString S2) {
    int i;
    if (S1[0] + S2[0] <= MAXSTRLEN) {
        for (i = 1; i <= S1[0]; i++) {
            T[i] = S1[i];
        }
        for (i = 1; i <= S2[0]; i++) {
            T[S1[0] + i] = S2[i];
        }
        T[0] = S1[0] + S2[0];
        return OK;
    } else {
        for (i = 1; i <= S1[0]; i++) {
            T[i] = S1[i];
        }
        for (i = 1; i <= MAXSTRLEN - S1[0]; i++) {
            T[S1[0] + i] = S2[i];
        }
        T[0] = MAXSTRLEN;
        return ERROR;
    }
}

// 用Sub返回串S的第pos个字符起长度为len的子串
Status SubString(SString Sub, SString S, int pos, int len) {
    int i;
    if (pos < 1 || pos > S[0] || len < 0 || len > S[0] - pos + 1) {
        return ERROR;
    }
    for (i = 1; i <= len; i++) {
        Sub[i] = S[pos + i - 1];
    }
    Sub[0] = len;
    return OK;
}
```

### 1.4 串的模式匹配算法

#### 1. 简单模式匹配算法（BF算法）
```c
// 返回子串T在主串S中第pos个字符之后的位置
int Index_BF(SString S, SString T, int pos) {
    int i = pos;  // i用于主串S中当前位置下标
    int j = 1;    // j用于子串T中当前位置下标
    
    while (i <= S[0] && j <= T[0]) {
        if (S[i] == T[j]) {  // 继续比较后继字符
            ++i;
            ++j;
        } else {             // 指针后退重新开始匹配
            i = i - j + 2;   // i退回到上次匹配首位的下一位
            j = 1;           // j退回到子串T的首位
        }
    }
    
    if (j > T[0]) return i - T[0];  // 匹配成功
    else return 0;                  // 匹配失败
}
```
**时间复杂度**：O(nm)，其中n是主串长度，m是模式串长度

#### 2. KMP算法
```c
// 求模式串T的next函数值并存入数组next
void get_next(SString T, int next[]) {
    int i = 1, j = 0;
    next[1] = 0;
    
    while (i < T[0]) {
        if (j == 0 || T[i] == T[j]) {
            ++i;
            ++j;
            next[i] = j;
        } else {
            j = next[j];
        }
    }
}

// KMP模式匹配算法
int Index_KMP(SString S, SString T, int pos) {
    int i = pos;  // i用于主串S中当前位置下标
    int j = 1;    // j用于子串T中当前位置下标
    int next[MAXSTRLEN+1];
    
    get_next(T, next);  // 对模式串T作分析，得到next函数
    
    while (i <= S[0] && j <= T[0]) {
        if (j == 0 || S[i] == T[j]) {  // 继续比较后继字符
            ++i;
            ++j;
        } else {                       // 模式串向右移动
            j = next[j];               // i不变，j后退
        }
    }
    
    if (j > T[0]) return i - T[0];     // 匹配成功
    else return 0;                     // 匹配失败
}

// 求模式串T的nextval函数值并存入数组nextval
void get_nextval(SString T, int nextval[]) {
    int i = 1, j = 0;
    nextval[1] = 0;
    
    while (i < T[0]) {
        if (j == 0 || T[i] == T[j]) {
            ++i;
            ++j;
            if (T[i] != T[j]) nextval[i] = j;
            else nextval[i] = nextval[j];
        } else {
            j = nextval[j];
        }
    }
}
```
**时间复杂度**：O(n+m)

### 1.5 KMP算法详解

#### next数组的含义
- next[j] = k 表示：当模式串第j个字符失配时，应该用第k个字符继续与主串当前字符比较
- next[j] = 0 表示：模式串应该从第1个字符开始重新匹配

#### next数组计算示例
模式串：abcabca

| j | 1 | 2 | 3 | 4 | 5 | 6 | 7 |
|---|---|---|---|---|---|---|---|
| T[j] | a | b | c | a | b | c | a |
| next[j] | 0 | 1 | 1 | 1 | 2 | 3 | 4 |

#### nextval数组优化
nextval数组是对next数组的优化，避免了不必要的比较。

---

## 2. 数组

### 2.1 数组的基本概念

#### 定义
**数组**：由n(n≥1)个相同类型的数据元素构成的有限序列

#### 特点
- 数组的维数是固定的
- 数组的每一维的长度是固定的
- 数组元素的类型是相同的

### 2.2 数组的存储结构

#### 一维数组
```c
ElemType A[n];  // 一维数组A有n个元素
```
**地址计算**：LOC(A[i]) = LOC(A[0]) + i × sizeof(ElemType)

#### 二维数组
```c
ElemType A[m][n];  // 二维数组A有m行n列
```

**按行优先存储**：
LOC(A[i][j]) = LOC(A[0][0]) + (i × n + j) × sizeof(ElemType)

**按列优先存储**：
LOC(A[i][j]) = LOC(A[0][0]) + (j × m + i) × sizeof(ElemType)

### 2.3 特殊矩阵的压缩存储

#### 1. 对称矩阵
对于n×n的对称矩阵，只需存储上三角（或下三角）元素。

```c
// 对称矩阵压缩存储
#define N 100
typedef struct {
    ElemType data[N*(N+1)/2];  // 一维数组存储下三角元素
    int n;                     // 矩阵阶数
} SymMatrix;

// 下三角元素的地址映射
int getIndex(int i, int j, int n) {
    if (i >= j) {
        return i * (i + 1) / 2 + j;  // 下三角元素
    } else {
        return j * (j + 1) / 2 + i;  // 上三角元素（利用对称性）
    }
}
```

#### 2. 三角矩阵
**下三角矩阵**：上三角区域的元素都相同
```c
// 下三角矩阵压缩存储
typedef struct {
    ElemType data[N*(N+1)/2+1];  // 多一个位置存储常数c
    int n;
    ElemType c;  // 上三角区域的常数
} LowerTriMatrix;
```

#### 3. 三对角矩阵（带状矩阵）
只有主对角线和相邻的两条对角线上有非零元素。

```c
// 三对角矩阵压缩存储
typedef struct {
    ElemType data[3*N-2];  // 存储三条对角线上的元素
    int n;
} TriDiagMatrix;

// 地址映射函数
int getTriDiagIndex(int i, int j) {
    if (abs(i - j) <= 1) {  // 在三条对角线上
        return 2 * i + j;
    }
    return -1;  // 不在存储范围内
}
```

#### 4. 稀疏矩阵
非零元素个数远少于矩阵元素总数的矩阵。

**三元组表示法**：
```c
#define MAXSIZE 12500
typedef struct {
    int i, j;      // 行下标和列下标
    ElemType e;    // 元素值
} Triple;

typedef struct {
    Triple data[MAXSIZE + 1];  // 非零元三元组表，data[0]未用
    int mu, nu, tu;            // 矩阵的行数、列数和非零元个数
} TSMatrix;
```

**十字链表表示法**：
```c
typedef struct OLNode {
    int i, j;              // 该非零元的行和列下标
    ElemType e;            // 非零元值
    struct OLNode *right, *down;  // 该非零元所在行表和列表的后继链域
} OLNode, *OLink;

typedef struct {
    OLink *rhead, *chead;  // 行和列链表头指针向量
    int mu, nu, tu;        // 稀疏矩阵的行数、列数和非零元个数
} CrossList;
```

### 2.4 稀疏矩阵的运算

#### 矩阵转置
```c
// 稀疏矩阵的转置
Status TransposeSMatrix(TSMatrix M, TSMatrix *T) {
    int col, t, p, q;
    
    T->mu = M.nu;
    T->nu = M.mu;
    T->tu = M.tu;
    
    if (T->tu) {
        q = 1;
        for (col = 1; col <= M.nu; ++col) {
            for (p = 1; p <= M.tu; ++p) {
                if (M.data[p].j == col) {
                    T->data[q].i = M.data[p].j;
                    T->data[q].j = M.data[p].i;
                    T->data[q].e = M.data[p].e;
                    ++q;
                }
            }
        }
    }
    return OK;
}

// 快速转置算法
Status FastTransposeSMatrix(TSMatrix M, TSMatrix *T) {
    int col, t, p, q;
    int num[MAXSIZE], cpot[MAXSIZE];
    
    T->mu = M.nu;
    T->nu = M.mu;
    T->tu = M.tu;
    
    if (T->tu) {
        // 求M中每一列含非零元个数
        for (col = 1; col <= M.nu; ++col) num[col] = 0;
        for (t = 1; t <= M.tu; ++t) ++num[M.data[t].j];
        
        // 求第col列中第一个非零元在T.data中的序号
        cpot[1] = 1;
        for (col = 2; col <= M.nu; ++col) {
            cpot[col] = cpot[col-1] + num[col-1];
        }
        
        // 进行转置
        for (p = 1; p <= M.tu; ++p) {
            col = M.data[p].j;
            q = cpot[col];
            T->data[q].i = M.data[p].j;
            T->data[q].j = M.data[p].i;
            T->data[q].e = M.data[p].e;
            ++cpot[col];
        }
    }
    return OK;
}
```

---

## 3. 广义表

### 3.1 广义表的基本概念

#### 定义
**广义表**：是线性表的推广，其元素可以是单个元素（原子），也可以是广义表（子表）

#### 表示方法
- 空表：()
- 原子表：(a, b, c)
- 混合表：(a, (b, c), d)

#### 重要概念
- **表头（Head）**：第一个元素
- **表尾（Tail）**：除第一个元素外的其余元素组成的表
- **长度**：最外层包含元素的个数
- **深度**：括号的最大嵌套层数

### 3.2 广义表的存储结构

#### 头尾链表存储表示
```c
typedef enum {ATOM, LIST} ElemTag;  // ATOM==0：原子，LIST==1：子表

typedef struct GLNode {
    ElemTag tag;  // 公共部分，用于区分原子结点和表结点
    union {       // 原子结点和表结点的联合部分
        AtomType atom;  // atom是原子结点的值域，AtomType由用户定义
        struct {        // ptr是表结点的指针域，ptr.hp和ptr.tp分别指向表头和表尾
            struct GLNode *hp, *tp;
        } ptr;
    };
} *GList;
```

#### 扩展线性链表存储表示
```c
typedef struct GLNode {
    ElemTag tag;
    union {
        AtomType atom;
        struct GLNode *hp;  // 表头指针
    };
    struct GLNode *tp;      // 指向下一个元素
} *GList;
```

### 3.3 广义表的基本操作

```c
// 求广义表的长度
int GListLength(GList L) {
    if (!L) return 0;
    int len = 0;
    GList p = L;
    while (p) {
        len++;
        p = p->tp;
    }
    return len;
}

// 求广义表的深度
int GListDepth(GList L) {
    if (!L) return 1;  // 空表深度为1
    if (L->tag == ATOM) return 0;  // 原子深度为0
    
    int maxdepth = 0;
    GList p = L;
    while (p) {
        if (p->tag == LIST) {
            int depth = GListDepth(p->ptr.hp);
            if (depth > maxdepth) maxdepth = depth;
        }
        p = p->ptr.tp;
    }
    return maxdepth + 1;
}
```

---

## 4. 学习要点总结

### 4.1 必须掌握的概念
- [ ] 串的基本概念和术语
- [ ] KMP算法的原理和next数组计算
- [ ] 数组的存储方式（行优先、列优先）
- [ ] 特殊矩阵的压缩存储方法
- [ ] 广义表的概念和存储结构

### 4.2 必须会做的计算
- [ ] 手工计算next数组和nextval数组
- [ ] 计算数组元素的存储地址
- [ ] 对称矩阵、三角矩阵的地址映射
- [ ] 稀疏矩阵的转置运算

### 4.3 常见考试题型
1. **选择题**：KMP算法的时间复杂度、数组存储方式
2. **填空题**：next数组的值、地址计算公式
3. **简答题**：KMP算法的优势、矩阵压缩存储的原理
4. **算法题**：串匹配、矩阵运算

---

## 5. 自测练习

### 5.1 基础概念题
1. KMP算法相比BF算法有什么优势？
2. 对称矩阵压缩存储可以节省多少空间？
3. 广义表的长度和深度有什么区别？

### 5.2 计算题
1. 对于模式串"abaabc"，计算其next数组和nextval数组
2. 设二维数组A[1..10][1..20]按行优先存储，每个元素占4个字节，A[1][1]的地址为100，求A[5][6]的地址
3. 对于5×5的对称矩阵，按下三角存储，求元素a[3][1]在一维数组中的下标

### 5.3 算法题
1. 实现串的插入和删除操作
2. 设计算法判断两个稀疏矩阵是否相等
3. 实现广义表的复制算法

---

## 6. 记忆技巧

### 6.1 KMP算法记忆
"**失配向右移，next数组来帮忙**"
- 主串指针不回退
- 模式串根据next数组移动

### 6.2 矩阵存储记忆
"**行优先按行走，列优先按列走**"
- 行优先：A[i][j] = base + (i×n + j)×size
- 列优先：A[i][j] = base + (j×m + i)×size

### 6.3 压缩存储记忆
"**对称存一半，三角加常数，稀疏用三元**"

---

**学习建议**：
1. 重点掌握KMP算法的原理和实现
2. 多练习next数组的手工计算
3. 理解各种矩阵压缩存储的地址映射
4. 掌握稀疏矩阵的基本运算

# 第二阶段：线性结构基础笔记

## 📚 Day 3-6 学习内容

---

## 1. 线性表的基本概念

### 1.1 什么是线性表？
**定义**：线性表是由n(n≥0)个数据元素组成的有限序列

**特点**：
- 有且仅有一个开始节点和终端节点
- 除开始节点外，每个节点有且仅有一个直接前驱
- 除终端节点外，每个节点有且仅有一个直接后继

### 1.2 线性表的抽象数据类型
```
ADT List {
    数据对象：D = {ai | ai ∈ ElemSet, i=1,2,...,n, n≥0}
    数据关系：R1 = {<ai-1, ai> | ai-1, ai ∈ D, i=2,...,n}
    基本操作：
        InitList(&L)        // 初始化
        DestroyList(&L)     // 销毁
        ClearList(&L)       // 清空
        ListEmpty(L)        // 判空
        ListLength(L)       // 求长度
        GetElem(L, i, &e)   // 取元素
        LocateElem(L, e)    // 定位元素
        PriorElem(L, cur_e, &pre_e)  // 前驱
        NextElem(L, cur_e, &next_e)  // 后继
        ListInsert(&L, i, e)         // 插入
        ListDelete(&L, i, &e)        // 删除
        ListTraverse(L)              // 遍历
}
```

---

## 2. 顺序存储结构（数组/向量）

### 2.1 顺序表的定义
**顺序表**：用一组地址连续的存储单元依次存储线性表的数据元素

### 2.2 顺序表的存储结构
```c
#define MAXSIZE 100
#define OK 1
#define ERROR 0
typedef int Status;
typedef int ElemType;

typedef struct {
    ElemType data[MAXSIZE];  // 存储数据元素的数组
    int length;              // 线性表当前长度
} SqList;
```

### 2.3 地址计算公式
```
LOC(ai) = LOC(a1) + (i-1) × sizeof(ElemType)
```
- LOC(ai)：第i个元素的存储地址
- LOC(a1)：第一个元素的存储地址
- sizeof(ElemType)：每个元素占用的字节数

### 2.4 顺序表的基本操作

#### 插入操作
```c
Status ListInsert(SqList *L, int i, ElemType e) {
    // 在第i个位置插入元素e
    if (i < 1 || i > L->length + 1) return ERROR;  // 位置不合法
    if (L->length >= MAXSIZE) return ERROR;        // 存储空间已满

    for (int j = L->length; j >= i; j--) {         // 元素后移
        L->data[j] = L->data[j-1];
    }
    L->data[i-1] = e;                              // 插入新元素
    L->length++;                                   // 长度加1
    return OK;
}
```
**时间复杂度**：O(n)

#### 删除操作
```c
Status ListDelete(SqList *L, int i, ElemType *e) {
    // 删除第i个位置的元素，用e返回其值
    if (i < 1 || i > L->length) return ERROR;     // 位置不合法

    *e = L->data[i-1];                             // 取出被删除元素
    for (int j = i; j < L->length; j++) {          // 元素前移
        L->data[j-1] = L->data[j];
    }
    L->length--;                                   // 长度减1
    return OK;
}
```
**时间复杂度**：O(n)

#### 查找操作
```c
int LocateElem(SqList L, ElemType e) {
    // 查找值为e的元素，返回其位序
    for (int i = 0; i < L.length; i++) {
        if (L.data[i] == e) return i + 1;         // 返回位序（从1开始）
    }
    return 0;                                     // 未找到
}
```
**时间复杂度**：O(n)

### 2.5 顺序表的优缺点

#### 优点
1. **随机访问**：可以通过下标直接访问任意元素，时间复杂度O(1)
2. **存储密度高**：不需要额外的指针空间
3. **局部性好**：相邻元素在内存中连续存储，缓存命中率高

#### 缺点
1. **插入删除效率低**：需要移动大量元素，时间复杂度O(n)
2. **存储空间固定**：需要预先分配空间，可能造成浪费或不足
3. **内存要求高**：需要连续的内存空间

---

## 3. 链式存储结构（链表）

### 3.1 单链表的定义
**单链表**：用一组任意的存储单元存储线性表的数据元素

### 3.2 单链表的存储结构
```c
typedef struct LNode {
    ElemType data;      // 数据域
    struct LNode *next; // 指针域
} LNode, *LinkList;
```

### 3.3 单链表的基本操作

#### 初始化（带头节点）
```c
Status InitList(LinkList *L) {
    *L = (LNode*)malloc(sizeof(LNode));  // 创建头节点
    if (!(*L)) return ERROR;             // 内存分配失败
    (*L)->next = NULL;                   // 头节点指针域置空
    return OK;
}
```

#### 插入操作
```c
Status ListInsert(LinkList L, int i, ElemType e) {
    // 在第i个位置插入元素e
    LNode *p = L;               // p指向头节点
    int j = 0;

    while (p && j < i-1) {      // 寻找第i-1个节点
        p = p->next;
        j++;
    }
    if (!p || j > i-1) return ERROR;  // 位置不合法

    LNode *s = (LNode*)malloc(sizeof(LNode));  // 创建新节点
    if (!s) return ERROR;       // 内存分配失败
    s->data = e;
    s->next = p->next;          // 新节点指向原第i个节点
    p->next = s;                // 第i-1个节点指向新节点
    return OK;
}
```
**时间复杂度**：O(n)

#### 删除操作
```c
Status ListDelete(LinkList L, int i, ElemType *e) {
    // 删除第i个位置的元素
    LNode *p = L;               // p指向头节点
    int j = 0;

    while (p->next && j < i-1) { // 寻找第i-1个节点
        p = p->next;
        j++;
    }
    if (!(p->next) || j > i-1) return ERROR;  // 位置不合法

    LNode *q = p->next;         // q指向被删除节点
    *e = q->data;               // 保存被删除元素的值
    p->next = q->next;          // 修改指针
    free(q);                    // 释放节点空间
    return OK;
}
```
**时间复杂度**：O(n)

#### 查找操作
```c
LNode* LocateElem(LinkList L, ElemType e) {
    // 查找值为e的元素
    LNode *p = L->next;         // p指向第一个数据节点
    while (p && p->data != e) {
        p = p->next;
    }
    return p;                   // 找到返回指针，否则返回NULL
}
```
**时间复杂度**：O(n)

### 3.4 其他链表类型

#### 双向链表
```c
typedef struct DuLNode {
    ElemType data;
    struct DuLNode *prior;      // 前驱指针
    struct DuLNode *next;       // 后继指针
} DuLNode, *DuLinkList;
```

#### 循环链表
- **循环单链表**：最后一个节点的指针指向头节点
- **循环双链表**：形成双向循环

### 3.5 链表的优缺点

#### 优点
1. **插入删除效率高**：只需修改指针，时间复杂度O(1)
2. **动态分配**：可以根据需要动态申请和释放内存
3. **不需要连续内存**：可以充分利用内存空间

#### 缺点
1. **不支持随机访问**：只能顺序访问，时间复杂度O(n)
2. **存储开销大**：需要额外的指针空间
3. **局部性差**：节点在内存中可能不连续

---

## 4. 顺序表与链表的比较

### 4.1 性能对比表
| 操作 | 顺序表 | 链表 |
|------|--------|------|
| 随机访问 | O(1) | O(n) |
| 插入/删除（已知位置） | O(n) | O(1) |
| 查找 | O(n) | O(n) |
| 空间复杂度 | 低 | 高 |

### 4.2 选择原则

#### 选择顺序表的情况
- 频繁进行随机访问操作
- 对存储空间要求较高
- 很少进行插入和删除操作
- 数据规模相对固定

#### 选择链表的情况
- 频繁进行插入和删除操作
- 数据规模变化较大
- 不需要随机访问
- 内存空间充足

---

## 5. 静态链表

### 5.1 静态链表的概念
**静态链表**：用数组来描述链表，用数组下标来代替指针

### 5.2 静态链表的存储结构
```c
#define MAXSIZE 1000
typedef struct {
    ElemType data;
    int cur;                    // 游标（相当于指针）
} SLinkList[MAXSIZE];
```

### 5.3 静态链表的特点
- 在不支持指针的语言中实现链表
- 插入删除不需要移动元素
- 不能随机访问
- 容量固定

---

## 6. 线性表的应用

### 6.1 顺序表的应用
1. **数组操作**：矩阵运算、图像处理
2. **查找表**：有序表的二分查找
3. **栈和队列的顺序实现**

### 6.2 链表的应用
1. **动态内存管理**：操作系统的内存分配
2. **多项式运算**：稀疏多项式的表示
3. **图的邻接表表示**

---

## 7. 学习要点总结

### 7.1 必须掌握的概念
- [ ] 线性表的定义和特点
- [ ] 顺序存储和链式存储的区别
- [ ] 各种操作的时间复杂度
- [ ] 头节点的作用

### 7.2 必须会做的操作
- [ ] 手工模拟插入删除过程
- [ ] 计算地址和时间复杂度
- [ ] 选择合适的存储结构

### 7.3 常见考试题型
1. **选择题**：比较顺序表和链表的性能
2. **填空题**：计算地址或复杂度
3. **简答题**：分析优缺点和适用场景
4. **算法题**：实现基本操作

---

## 8. 自测练习

### 8.1 基础概念题
1. 线性表的特点是什么？
2. 顺序表和链表各有什么优缺点？
3. 头节点的作用是什么？

### 8.2 计算题
1. 在长度为n的顺序表中插入一个元素，平均需要移动多少个元素？
2. 设顺序表第一个元素的地址为100，每个元素占4个字节，求第10个元素的地址。

### 8.3 算法设计题
1. 设计算法删除顺序表中所有值为x的元素
2. 设计算法将两个有序链表合并为一个有序链表

---

## 9. 记忆技巧

### 9.1 顺序表vs链表口诀
"**顺序随机快，链表插删强**"
- 顺序表：随机访问快
- 链表：插入删除强

### 9.2 操作复杂度记忆
"**顺序一查多，链表多查一**"
- 顺序表：访问O(1)，插删O(n)
- 链表：访问O(n)，插删O(1)

---

**学习建议**：
1. 重点理解两种存储方式的本质区别
2. 多画图理解指针操作
3. 手工模拟各种操作过程
4. 结合实际应用场景选择存储结构

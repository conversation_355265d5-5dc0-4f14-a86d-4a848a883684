# 第七阶段：查找树与排序算法笔记 - 基础

## 📚 Day 15-18 学习内容

---

## 1. 二叉查找树（BST）

### 1.1 二叉查找树的定义

#### 定义
**二叉查找树**：或者是一棵空树，或者是具有下列性质的二叉树：
1. 若左子树不空，则左子树上所有节点的值均小于根节点的值
2. 若右子树不空，则右子树上所有节点的值均大于根节点的值
3. 左、右子树也分别为二叉查找树

#### 特点
- 中序遍历二叉查找树可得到一个递增的有序序列
- 查找、插入、删除的平均时间复杂度为O(log n)
- 最坏情况下退化为链表，时间复杂度为O(n)

### 1.2 二叉查找树的存储结构

```c
#include <stdio.h>
#include <stdlib.h>
#define OK 1
#define ERROR 0
typedef int Status;
typedef int KeyType;

typedef struct BSTNode {
    KeyType key;                    // 关键字
    struct BSTNode *lchild, *rchild;  // 左右孩子指针
} BSTNode, *BSTree;
```

### 1.3 二叉查找树的基本操作

#### 1. 查找操作
```c
// 递归查找
BSTNode* BST_Search(BSTree T, KeyType key) {
    if (T == NULL || key == T->key) {
        return T;  // 查找成功或失败
    } else if (key < T->key) {
        return BST_Search(T->lchild, key);  // 在左子树中查找
    } else {
        return BST_Search(T->rchild, key);  // 在右子树中查找
    }
}

// 非递归查找
BSTNode* BST_Search_Iterative(BSTree T, KeyType key) {
    while (T != NULL && key != T->key) {
        if (key < T->key) {
            T = T->lchild;
        } else {
            T = T->rchild;
        }
    }
    return T;
}
```

#### 2. 插入操作
```c
// 递归插入
Status BST_Insert(BSTree *T, KeyType key) {
    if (*T == NULL) {
        *T = (BSTNode*)malloc(sizeof(BSTNode));
        (*T)->key = key;
        (*T)->lchild = (*T)->rchild = NULL;
        return OK;
    } else if (key == (*T)->key) {
        return ERROR;  // 树中存在相同关键字的节点，插入失败
    } else if (key < (*T)->key) {
        return BST_Insert(&((*T)->lchild), key);
    } else {
        return BST_Insert(&((*T)->rchild), key);
    }
}

// 非递归插入
Status BST_Insert_Iterative(BSTree *T, KeyType key) {
    BSTNode *p = *T, *parent = NULL;
    
    // 查找插入位置
    while (p != NULL) {
        parent = p;
        if (key == p->key) return ERROR;  // 关键字已存在
        else if (key < p->key) p = p->lchild;
        else p = p->rchild;
    }
    
    // 创建新节点
    BSTNode *newNode = (BSTNode*)malloc(sizeof(BSTNode));
    newNode->key = key;
    newNode->lchild = newNode->rchild = NULL;
    
    // 插入新节点
    if (parent == NULL) {
        *T = newNode;  // 树为空，新节点成为根节点
    } else if (key < parent->key) {
        parent->lchild = newNode;
    } else {
        parent->rchild = newNode;
    }
    
    return OK;
}
```

#### 3. 删除操作
```c
// 删除节点
Status BST_Delete(BSTree *T, KeyType key) {
    if (*T == NULL) return ERROR;  // 不存在关键字等于key的数据元素
    
    if (key == (*T)->key) {
        return Delete(T);  // 调用Delete函数删除节点*T
    } else if (key < (*T)->key) {
        return BST_Delete(&((*T)->lchild), key);
    } else {
        return BST_Delete(&((*T)->rchild), key);
    }
}

// 删除节点的具体实现
Status Delete(BSTree *p) {
    BSTNode *q, *s;
    
    if ((*p)->rchild == NULL) {        // 右子树空则只需重接它的左子树
        q = *p;
        *p = (*p)->lchild;
        free(q);
    } else if ((*p)->lchild == NULL) { // 左子树空则只需重接它的右子树
        q = *p;
        *p = (*p)->rchild;
        free(q);
    } else {                           // 左右子树均不空
        q = *p;
        s = (*p)->lchild;
        while (s->rchild) {            // 转左，然后向右到尽头
            q = s;
            s = s->rchild;
        }
        (*p)->key = s->key;            // s指向被删节点的直接前驱
        if (q != *p) {
            q->rchild = s->lchild;     // 重接q的右子树
        } else {
            q->lchild = s->lchild;     // 重接q的左子树
        }
        free(s);
    }
    return OK;
}
```

### 1.4 二叉查找树的构造

```c
// 构造二叉查找树
void CreateBST(BSTree *T, KeyType keys[], int n) {
    *T = NULL;
    for (int i = 0; i < n; i++) {
        BST_Insert(T, keys[i]);
    }
}

// 中序遍历（验证BST性质）
void InOrder_BST(BSTree T) {
    if (T) {
        InOrder_BST(T->lchild);
        printf("%d ", T->key);
        InOrder_BST(T->rchild);
    }
}
```

---

## 2. 平衡二叉树（AVL树）

### 2.1 AVL树的定义

#### 定义
**AVL树**：是一棵空树或它的左右两个子树的高度差的绝对值不超过1，并且左右两个子树都是一棵平衡二叉树

#### 平衡因子
**平衡因子BF**：节点的左子树高度减去右子树高度的值
- BF ∈ {-1, 0, 1}
- |BF| > 1 时需要进行旋转调整

### 2.2 AVL树的存储结构

```c
typedef struct AVLNode {
    KeyType key;
    int bf;                           // 平衡因子
    struct AVLNode *lchild, *rchild;
} AVLNode, *AVLTree;
```

### 2.3 AVL树的旋转操作

#### 1. 右旋转（LL型调整）
```c
// 右旋转
void R_Rotate(AVLTree *p) {
    AVLNode *lc = (*p)->lchild;       // lc指向*p的左子树根节点
    (*p)->lchild = lc->rchild;        // lc的右子树挂接为*p的左子树
    lc->rchild = *p;
    *p = lc;                          // *p指向新的根节点
}
```

#### 2. 左旋转（RR型调整）
```c
// 左旋转
void L_Rotate(AVLTree *p) {
    AVLNode *rc = (*p)->rchild;       // rc指向*p的右子树根节点
    (*p)->rchild = rc->lchild;        // rc的左子树挂接为*p的右子树
    rc->lchild = *p;
    *p = rc;                          // *p指向新的根节点
}
```

#### 3. 左平衡处理（LR型调整）
```c
// 左平衡处理
void LeftBalance(AVLTree *T) {
    AVLNode *lc = (*T)->lchild;       // lc指向*T的左子树根节点
    
    switch (lc->bf) {                 // 检查*T的左子树的平衡度，并作相应平衡处理
        case 1:                       // 新节点插入在*T的左孩子的左子树上，要作单右旋处理
            (*T)->bf = lc->bf = 0;
            R_Rotate(T);
            break;
        case -1:                      // 新节点插入在*T的左孩子的右子树上，要作双旋处理
            AVLNode *rd = lc->rchild; // rd指向*T的左孩子的右子树根
            switch (rd->bf) {         // 修改*T及其左孩子的平衡因子
                case 1:
                    (*T)->bf = -1;
                    lc->bf = 0;
                    break;
                case 0:
                    (*T)->bf = lc->bf = 0;
                    break;
                case -1:
                    (*T)->bf = 0;
                    lc->bf = 1;
                    break;
            }
            rd->bf = 0;
            L_Rotate(&((*T)->lchild)); // 对*T的左子树作左旋平衡处理
            R_Rotate(T);               // 对*T作右旋平衡处理
    }
}
```

#### 4. 右平衡处理（RL型调整）
```c
// 右平衡处理
void RightBalance(AVLTree *T) {
    AVLNode *rc = (*T)->rchild;       // rc指向*T的右子树根节点
    
    switch (rc->bf) {                 // 检查*T的右子树的平衡度，并作相应平衡处理
        case -1:                      // 新节点插入在*T的右孩子的右子树上，要作单左旋处理
            (*T)->bf = rc->bf = 0;
            L_Rotate(T);
            break;
        case 1:                       // 新节点插入在*T的右孩子的左子树上，要作双旋处理
            AVLNode *ld = rc->lchild; // ld指向*T的右孩子的左子树根
            switch (ld->bf) {         // 修改*T及其右孩子的平衡因子
                case 1:
                    (*T)->bf = 0;
                    rc->bf = -1;
                    break;
                case 0:
                    (*T)->bf = rc->bf = 0;
                    break;
                case -1:
                    (*T)->bf = 1;
                    rc->bf = 0;
                    break;
            }
            ld->bf = 0;
            R_Rotate(&((*T)->rchild)); // 对*T的右子树作右旋平衡处理
            L_Rotate(T);               // 对*T作左旋平衡处理
    }
}
```

### 2.4 AVL树的插入操作

```c
// AVL树的插入
Status InsertAVL(AVLTree *T, KeyType key, int *taller) {
    if (*T == NULL) {                 // 插入新节点，树"长高"，置taller为TRUE
        *T = (AVLNode*)malloc(sizeof(AVLNode));
        (*T)->key = key;
        (*T)->lchild = (*T)->rchild = NULL;
        (*T)->bf = 0;
        *taller = 1;
    } else {
        if (key == (*T)->key) {       // 树中存在和key有相同关键字的节点，则不再插入
            *taller = 0;
            return ERROR;
        }
        if (key < (*T)->key) {        // 应继续在*T的左子树中进行搜索
            if (!InsertAVL(&((*T)->lchild), key, taller)) return ERROR;
            if (*taller) {            // 已插入到*T的左子树中且左子树"长高"
                switch ((*T)->bf) {   // 检查*T的平衡度
                    case 1:           // 原本左子树比右子树高，现在更不平衡了
                        LeftBalance(T);
                        *taller = 0;
                        break;
                    case 0:           // 原本左、右子树等高，现在左子树增高而使树增高
                        (*T)->bf = 1;
                        *taller = 1;
                        break;
                    case -1:          // 原本右子树比左子树高，现在左、右子树等高
                        (*T)->bf = 0;
                        *taller = 0;
                        break;
                }
            }
        } else {                      // 应继续在*T的右子树中进行搜索
            if (!InsertAVL(&((*T)->rchild), key, taller)) return ERROR;
            if (*taller) {            // 已插入到*T的右子树且右子树"长高"
                switch ((*T)->bf) {   // 检查*T的平衡度
                    case 1:           // 原本左子树比右子树高，现在左、右子树等高
                        (*T)->bf = 0;
                        *taller = 0;
                        break;
                    case 0:           // 原本左、右子树等高，现在右子树增高而使树增高
                        (*T)->bf = -1;
                        *taller = 1;
                        break;
                    case -1:          // 原本右子树比左子树高，现在更不平衡了
                        RightBalance(T);
                        *taller = 0;
                        break;
                }
            }
        }
    }
    return OK;
}
```

---

## 3. 学习要点总结

### 3.1 必须掌握的概念
- [ ] 二叉查找树的定义和性质
- [ ] AVL树的平衡因子概念
- [ ] 四种旋转操作的类型
- [ ] BST和AVL树的时间复杂度

### 3.2 必须会做的操作
- [ ] 手工构造二叉查找树
- [ ] 判断AVL树的平衡性
- [ ] 进行旋转操作调整平衡
- [ ] 分析查找、插入、删除的过程

### 3.3 常见考试题型
1. **选择题**：BST和AVL树的性质
2. **填空题**：平衡因子计算、旋转类型判断
3. **简答题**：BST删除操作的三种情况
4. **算法题**：AVL树的插入和旋转

---

## 4. 记忆技巧

### 4.1 BST性质记忆
"**左小右大中序升**"
- 左子树 < 根节点 < 右子树
- 中序遍历得到升序序列

### 4.2 AVL旋转记忆
"**LL右旋，RR左旋，LR先左后右，RL先右后左**"

### 4.3 平衡因子记忆
"**左减右，范围一**"
- BF = 左子树高度 - 右子树高度
- BF ∈ {-1, 0, 1}

---

**学习建议**：
1. 重点掌握BST的三种删除情况
2. 理解AVL树旋转操作的本质
3. 多练习手工构造和调整AVL树
4. 掌握平衡因子的计算和更新

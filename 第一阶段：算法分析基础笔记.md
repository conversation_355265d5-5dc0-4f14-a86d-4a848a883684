# 第一阶段：算法分析基础笔记

## 📚 Day 1-2 学习内容

---

## 1. 算法的基本概念

### 1.1 什么是算法？
**定义**：算法是解决特定问题的有限步骤序列

**算法的五个特性**：
1. **有穷性**：算法必须在有限步骤内结束
2. **确定性**：每一步都有明确的定义，不能有歧义
3. **可行性**：每一步都是可以执行的
4. **输入**：有零个或多个输入
5. **输出**：有一个或多个输出

### 1.2 算法与程序的区别
| 算法 | 程序 |
|------|------|
| 抽象的解决方案 | 具体的实现代码 |
| 与语言无关 | 依赖特定语言 |
| 描述解决思路 | 可以在计算机上运行 |

### 1.3 算法设计的要求
1. **正确性**：算法应该能够正确解决问题

2. **可读性**：算法应该容易理解和实现

3. **健壮性**：算法应该能处理异常情况

4. **效率性**：算法应该尽可能高效

   

---

## 2. 算法复杂度分析

### 2.1 为什么要分析复杂度？
- **选择最优算法**：比较不同算法的效率
- **预测性能**：估算算法在大数据量下的表现
- **资源规划**：合理分配计算资源

### 2.2 时间复杂度

#### 基本概念
**时间复杂度**：算法执行时间与输入规模n的函数关系

#### 大O记号（Big O Notation）
- **定义**：T(n) = O(f(n))，表示T(n)的增长率不超过f(n)
- **含义**：描述算法在最坏情况下的时间复杂度上界

#### 常见复杂度等级（从优到劣）
```c
O(1) < O(log n) < O(n) < O(n log n) < O(n²) < O(n³) < O(2ⁿ) < O(n!)
```

**具体解释**：
1. **O(1) - 常数时间**
   - 例子：数组按索引访问 `arr[5]`
   - 特点：执行时间不随输入规模变化

2. **O(log n) - 对数时间**
   - 例子：二分查找
   - 特点：每次操作将问题规模减半

3. **O(n) - 线性时间**
   - 例子：遍历数组
   - 特点：执行时间与输入规模成正比

4. **O(n log n) - 线性对数时间**
   - 例子：归并排序、快速排序（平均情况）
   - 特点：高效排序算法的典型复杂度

5. **O(n²) - 平方时间**
   - 例子：冒泡排序、选择排序
   - 特点：嵌套循环的典型复杂度

6. **O(2ⁿ) - 指数时间**
   - 例子：递归求斐波那契数列
   - 特点：非常低效，只适用于小规模问题

#### 复杂度计算规则
1. **忽略常数项**：O(3n) = O(n)
2. **忽略低次项**：O(n² + n) = O(n²)
3. **忽略系数**：O(2n²) = O(n²)
4. **保留最高次项**：O(n³ + n² + n + 1) = O(n³)

### 2.3 空间复杂度

#### 基本概念
**空间复杂度**：算法执行过程中所需要的存储空间与输入规模n的函数关系

#### 空间复杂度的组成
1. **固定空间**：算法本身占用的空间（常数）
2. **可变空间**：随输入规模变化的空间
   - 输入数据占用的空间
   - 算法执行过程中的临时空间

#### 常见空间复杂度
- **O(1)**：只使用常数个额外变量
- **O(n)**：需要与输入规模成正比的额外空间
- **O(log n)**：递归调用栈的深度为log n

---

## 3. 递归算法

### 3.1 递归的基本概念

#### 什么是递归？
**递归**：函数直接或间接调用自身的编程技巧

#### 递归的两个要素
1. **递归基（Base Case）**：递归终止的条件
2. **递归步（Recursive Step）**：将问题分解为更小的子问题

### 3.2 递归算法的设计步骤
1. **确定递归基**：什么时候停止递归
2. **确定递归关系**：如何将大问题分解为小问题
3. **确保递归收敛**：每次递归都向递归基靠近

### 3.3 递归算法示例

#### 示例1：计算阶乘
```c
factorial(n) = 
    if n == 0 or n == 1:
        return 1          // 递归基
    else:
        return n * factorial(n-1)  // 递归步
```

**执行过程**：
```c
factorial(4)
= 4 * factorial(3)
= 4 * (3 * factorial(2))
= 4 * (3 * (2 * factorial(1)))
= 4 * (3 * (2 * 1))
= 4 * (3 * 2)
= 4 * 6
= 24
```

#### 示例2：斐波那契数列
```c
fibonacci(n) = 
    if n == 0:
        return 0          // 递归基
    if n == 1:
        return 1          // 递归基
    else:
        return fibonacci(n-1) + fibonacci(n-2)  // 递归步
```

### 3.4 递归的优缺点

#### 优点
1. **代码简洁**：递归代码通常比迭代代码更简洁
2. **逻辑清晰**：递归的思路更接近问题的数学定义
3. **易于理解**：对于本质上递归的问题，递归解法更直观

#### 缺点
1. **空间开销大**：每次递归调用都要保存函数状态
2. **可能栈溢出**：递归层数过多会导致栈空间不足
3. **效率可能较低**：重复计算相同子问题（如斐波那契）

### 3.5 尾递归优化

#### 什么是尾递归？
**尾递归**：递归调用是函数的最后一个操作

#### 尾递归的优势
- 可以被编译器优化为循环
- 不会增加调用栈的深度
- 避免栈溢出问题

#### 尾递归示例：阶乘
```
factorial_tail(n, acc) = 
    if n == 0 or n == 1:
        return acc
    else:
        return factorial_tail(n-1, n * acc)

factorial(n) = factorial_tail(n, 1)
```

### 3.6 递归转迭代的方法
1. **使用栈模拟递归**：手动维护调用栈
2. **找出递推关系**：将递归关系转换为循环
3. **动态规划**：避免重复计算子问题

---

## 4. 复杂度分析实例

### 4.1 简单循环
```
for i = 1 to n:
    print(i)
```
**分析**：循环执行n次，每次O(1)，总复杂度O(n)

### 4.2 嵌套循环
```
for i = 1 to n:
    for j = 1 to n:
        print(i, j)
```
**分析**：外层循环n次，内层循环n次，总复杂度O(n²)

### 4.3 递归算法复杂度
#### 递归关系式
对于递归算法，通常用递归关系式表示：
- T(n) = aT(n/b) + f(n)

#### 主定理（Master Theorem）
对于形如 T(n) = aT(n/b) + f(n) 的递归关系：
1. 如果 f(n) = O(n^c)，其中 c < log_b(a)，则 T(n) = O(n^(log_b(a)))
2. 如果 f(n) = O(n^c)，其中 c = log_b(a)，则 T(n) = O(n^c * log n)
3. 如果 f(n) = O(n^c)，其中 c > log_b(a)，则 T(n) = O(f(n))

---

## 5. 学习要点总结

### 5.1 必须掌握的概念
- [ ] 算法的五个特性
- [ ] 时间复杂度和空间复杂度的含义
- [ ] 大O记号的使用
- [ ] 常见复杂度等级的比较
- [ ] 递归的基本原理

### 5.2 必须会做的计算
- [ ] 简单算法的复杂度分析
- [ ] 递归算法的复杂度计算
- [ ] 比较不同算法的效率

### 5.3 常见考试题型
1. **选择题**：比较算法复杂度
2. **填空题**：计算具体算法的复杂度
3. **简答题**：解释递归的优缺点
4. **算法题**：设计递归算法

---

## 6. 自测练习

### 6.1 基础概念题
1. 算法的五个特性是什么？
2. 时间复杂度O(n²)和O(2n)哪个更优？
3. 什么是尾递归？有什么优势？

### 6.2 复杂度计算题
1. 计算下列代码的时间复杂度：
```
for i = 1 to n:
    for j = i to n:
        print(i, j)
```

2. 分析二分查找的时间复杂度

### 6.3 递归设计题
1. 设计递归算法计算x^n
2. 用递归实现数组求和

---

## 7. 记忆口诀

### 复杂度等级口诀
"**常对线方立指阶**"
- 常：O(1) 常数
- 对：O(log n) 对数  
- 线：O(n) 线性
- 方：O(n²) 平方
- 立：O(n³) 立方
- 指：O(2ⁿ) 指数
- 阶：O(n!) 阶乘

### 递归设计口诀
"**基步敛**"
- 基：确定递归基
- 步：设计递归步
- 敛：保证收敛性

---

**学习建议**：
1. 每天花30分钟练习复杂度计算
2. 手工模拟递归算法的执行过程
3. 多做对比，理解不同复杂度的差异
4. 重点理解递归的思想，这是后续学习的基础
